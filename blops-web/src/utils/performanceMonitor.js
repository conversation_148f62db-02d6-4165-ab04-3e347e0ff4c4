// 性能监控工具
class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.observers = [];
  }

  // 开始性能测量
  startMeasure(name) {
    const startTime = performance.now();
    this.metrics.set(name, { startTime, endTime: null, duration: null });
    return startTime;
  }

  // 结束性能测量
  endMeasure(name) {
    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`Performance measure "${name}" not found`);
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - metric.startTime;
    
    metric.endTime = endTime;
    metric.duration = duration;
    
    this.metrics.set(name, metric);
    
    // 通知观察者
    this.notifyObservers(name, metric);
    
    return duration;
  }

  // 获取性能指标
  getMeasure(name) {
    return this.metrics.get(name);
  }

  // 获取所有性能指标
  getAllMeasures() {
    return Object.fromEntries(this.metrics);
  }

  // 添加性能观察者
  addObserver(callback) {
    this.observers.push(callback);
  }

  // 移除性能观察者
  removeObserver(callback) {
    const index = this.observers.indexOf(callback);
    if (index > -1) {
      this.observers.splice(index, 1);
    }
  }

  // 通知观察者
  notifyObservers(name, metric) {
    this.observers.forEach(callback => {
      try {
        callback(name, metric);
      } catch (error) {
        console.error('Performance observer error:', error);
      }
    });
  }

  // 清除所有指标
  clear() {
    this.metrics.clear();
  }

  // 记录渲染性能
  measureRender(componentName, renderFn) {
    const measureName = `render_${componentName}`;
    this.startMeasure(measureName);
    
    const result = renderFn();
    
    // 使用 requestAnimationFrame 确保在渲染完成后测量
    requestAnimationFrame(() => {
      const duration = this.endMeasure(measureName);
      if (duration > 16) { // 超过一帧的时间
        console.warn(`Slow render detected for ${componentName}: ${duration.toFixed(2)}ms`);
      }
    });
    
    return result;
  }

  // 记录数据处理性能
  measureDataProcessing(operationName, processFn) {
    const measureName = `data_${operationName}`;
    this.startMeasure(measureName);
    
    const result = processFn();
    
    const duration = this.endMeasure(measureName);
    if (duration > 100) { // 超过100ms
      console.warn(`Slow data processing detected for ${operationName}: ${duration.toFixed(2)}ms`);
    }
    
    return result;
  }

  // 生成性能报告
  generateReport() {
    const measures = this.getAllMeasures();
    const report = {
      timestamp: new Date().toISOString(),
      totalMeasures: Object.keys(measures).length,
      slowOperations: [],
      averageTimes: {},
      summary: {}
    };

    // 分析慢操作
    Object.entries(measures).forEach(([name, metric]) => {
      if (metric.duration > 100) {
        report.slowOperations.push({
          name,
          duration: metric.duration,
          startTime: metric.startTime,
          endTime: metric.endTime
        });
      }
    });

    // 计算平均时间（按操作类型分组）
    const groupedMeasures = {};
    Object.entries(measures).forEach(([name, metric]) => {
      const type = name.split('_')[0];
      if (!groupedMeasures[type]) {
        groupedMeasures[type] = [];
      }
      groupedMeasures[type].push(metric.duration);
    });

    Object.entries(groupedMeasures).forEach(([type, durations]) => {
      const average = durations.reduce((sum, d) => sum + d, 0) / durations.length;
      report.averageTimes[type] = {
        average: average.toFixed(2),
        count: durations.length,
        min: Math.min(...durations).toFixed(2),
        max: Math.max(...durations).toFixed(2)
      };
    });

    // 生成摘要
    report.summary = {
      totalSlowOperations: report.slowOperations.length,
      performanceScore: this.calculatePerformanceScore(measures),
      recommendations: this.generateRecommendations(report)
    };

    return report;
  }

  // 计算性能分数 (0-100)
  calculatePerformanceScore(measures) {
    const durations = Object.values(measures).map(m => m.duration);
    if (durations.length === 0) return 100;

    const averageDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length;
    const slowOperationsCount = durations.filter(d => d > 100).length;
    const slowOperationsRatio = slowOperationsCount / durations.length;

    // 基础分数从平均响应时间计算
    let score = Math.max(0, 100 - (averageDuration / 10));
    
    // 根据慢操作比例扣分
    score -= slowOperationsRatio * 30;
    
    return Math.max(0, Math.min(100, Math.round(score)));
  }

  // 生成性能建议
  generateRecommendations(report) {
    const recommendations = [];

    if (report.slowOperations.length > 0) {
      recommendations.push('检测到慢操作，建议优化数据处理逻辑');
    }

    if (report.averageTimes.render && parseFloat(report.averageTimes.render.average) > 16) {
      recommendations.push('渲染时间过长，建议使用React.memo或useMemo优化');
    }

    if (report.averageTimes.data && parseFloat(report.averageTimes.data.average) > 50) {
      recommendations.push('数据处理时间过长，建议使用Web Workers或分批处理');
    }

    if (recommendations.length === 0) {
      recommendations.push('性能表现良好，继续保持');
    }

    return recommendations;
  }
}

// 创建全局实例
const performanceMonitor = new PerformanceMonitor();

// 开发环境下启用详细日志
if (process.env.NODE_ENV === 'development') {
  performanceMonitor.addObserver((name, metric) => {
    console.log(`Performance: ${name} took ${metric.duration.toFixed(2)}ms`);
  });
}

export default performanceMonitor;
