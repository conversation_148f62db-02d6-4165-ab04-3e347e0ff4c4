.sync-manager {
  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    
    .ant-card-head {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 8px 8px 0 0;
      
      .ant-card-head-title {
        color: white;
        font-weight: 600;
      }
      
      .ant-card-extra {
        .ant-btn {
          border-color: rgba(255, 255, 255, 0.3);
          color: white;
          
          &:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
          }
          
          &.ant-btn-primary {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
            
            &:hover {
              background: rgba(255, 255, 255, 0.3);
              border-color: rgba(255, 255, 255, 0.5);
            }
          }
        }
      }
    }
  }

  .ant-table {
    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 600;
      color: #262626;
    }

    .ant-table-tbody > tr:hover > td {
      background: #f0f9ff;
    }
  }

  .ant-progress {
    .ant-progress-text {
      font-size: 12px;
      font-weight: 500;
    }
  }

  .ant-tag {
    border-radius: 4px;
    font-weight: 500;
    
    &.ant-tag-blue {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      color: white;
    }
    
    &.ant-tag-orange {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      border: none;
      color: white;
    }
    
    &.ant-tag-success {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      border: none;
      color: white;
    }
    
    &.ant-tag-processing {
      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      border: none;
      color: white;
    }
    
    &.ant-tag-error {
      background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
      border: none;
      color: white;
    }
    
    &.ant-tag-warning {
      background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
      border: none;
      color: #8b4513;
    }
  }

  .ant-badge {
    .ant-badge-count {
      font-size: 10px;
      min-width: 16px;
      height: 16px;
      line-height: 16px;
      border-radius: 8px;
      box-shadow: 0 0 0 1px #fff;
    }
  }

  .ant-modal {
    .ant-modal-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 8px 8px 0 0;
      
      .ant-modal-title {
        color: white;
        font-weight: 600;
      }
    }
    
    .ant-modal-close {
      color: rgba(255, 255, 255, 0.8);
      
      &:hover {
        color: white;
      }
    }
  }

  .ant-descriptions {
    .ant-descriptions-item-label {
      font-weight: 600;
      color: #262626;
    }
    
    &.ant-descriptions-bordered {
      .ant-descriptions-item-label {
        background: #fafafa;
      }
    }
  }

  .ant-statistic {
    .ant-statistic-title {
      font-size: 12px;
      color: #8c8c8c;
      font-weight: 500;
    }
    
    .ant-statistic-content {
      font-size: 20px;
      font-weight: 600;
      color: #262626;
    }
  }

  .ant-tabs {
    .ant-tabs-tab {
      font-weight: 500;
      
      &.ant-tabs-tab-active {
        .ant-tabs-tab-btn {
          color: #667eea;
          font-weight: 600;
        }
      }
    }
    
    .ant-tabs-ink-bar {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
  }

  .ant-list {
    .ant-list-item {
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
    }
  }

  // 自定义样式
  .sync-stats {
    .ant-col {
      text-align: center;
      padding: 16px;
      
      &:not(:last-child) {
        border-right: 1px solid #f0f0f0;
      }
    }
  }

  .preview-empty {
    text-align: center;
    padding: 40px;
    color: #999;
    
    .anticon {
      font-size: 48px;
      margin-bottom: 16px;
      color: #d9d9d9;
    }
  }

  .form-tip {
    padding: 12px;
    background: #f6f8fa;
    border-radius: 6px;
    font-size: 12px;
    color: #666;
    border-left: 3px solid #667eea;
    
    .anticon {
      margin-right: 8px;
      color: #667eea;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .ant-table {
      .ant-table-content {
        overflow-x: auto;
      }
    }
    
    .ant-modal {
      margin: 0;
      max-width: 100vw;
      
      .ant-modal-content {
        border-radius: 0;
      }
    }
  }

  // 动画效果
  .ant-btn {
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  .ant-card {
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    
    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    }
  }

  .ant-table-tbody > tr {
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  }
}
