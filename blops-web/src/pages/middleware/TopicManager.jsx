import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Card,
  Tabs,
  Button,
  Table,
  Modal,
  Form,
  Input,
  Space,
  Select,
  Popconfirm,
  message,
  Spin,
  Empty,
  Tag,
  Descriptions,
  Row,
  Col,
  Statistic,
  Divider,
  Typography,
  Badge,
  Tooltip,
  Avatar,
  Pagination
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  DatabaseOutlined,
  TeamOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ApiOutlined,
  ClusterOutlined,
  SyncOutlined
} from '@ant-design/icons';
import { request } from 'umi';
import PerformantTable from '@/components/PerformantTable';
import SyncManager from './SyncManager';
import performanceMonitor from '@/utils/performanceMonitor';
import './TopicManager.less';

const { TabPane } = Tabs;
const { Option } = Select;
const { Title, Text } = Typography;

const TopicManager = () => {
  // 状态管理
  const [envList, setEnvList] = useState([]);
  const [configList, setConfigList] = useState([]);
  const [topicsAndGroups, setTopicsAndGroups] = useState({});
  const [loading, setLoading] = useState(false);
  const [configModalVisible, setConfigModalVisible] = useState(false);
  const [topicModalVisible, setTopicModalVisible] = useState(false);
  const [currentConfig, setCurrentConfig] = useState(null);
  const [currentTopicConfig, setCurrentTopicConfig] = useState(null);
  const [configForm] = Form.useForm();
  const [activeKey, setActiveKey] = useState('1');
  const [testingConnection, setTestingConnection] = useState(false);
  const [topicsLoading, setTopicsLoading] = useState(false);



  // 初始化加载数据
  useEffect(() => {
    fetchEnvList();
    fetchConfigList();
  }, []);

  // 获取环境列表
  const fetchEnvList = async () => {
    try {
      const res = await request('/api/v1/middleware/env', {
        method: 'GET',
      });
      if (res.result === 'SUCCESS') {
        setEnvList(res.data || []);
      } else {
        message.error('获取环境列表失败');
      }
    } catch (error) {
      message.error('获取环境列表出错');
      console.error(error);
    }
  };

  // 获取Topic配置列表
  const fetchConfigList = async () => {
    setLoading(true);
    try {
      const res = await request('/api/v1/topic/config?with_env_info=true', {
        method: 'GET',
      });
      if (res.result === 'SUCCESS') {
        setConfigList(res.data || []);
      } else {
        message.error('获取Topic配置列表失败');
      }
    } catch (error) {
      message.error('获取Topic配置列表出错');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 获取Topic和Group信息（带性能监控）
  const fetchTopicsAndGroups = async (configId) => {
    performanceMonitor.startMeasure('fetch_topics_groups');
    setTopicsLoading(true);
    try {
      const res = await request(`/api/v1/topic/config/${configId}/topics-groups`, {
        method: 'GET',
      });
      if (res.result === 'SUCCESS') {
        // 监控数据处理性能
        performanceMonitor.measureDataProcessing('process_topics_groups', () => {
          setTopicsAndGroups(prev => ({
            ...prev,
            [configId]: res.data
          }));
        });

        const duration = performanceMonitor.endMeasure('fetch_topics_groups');
        console.log(`Topics and Groups loaded in ${duration?.toFixed(2)}ms`);
      } else {
        message.error(res.message || '获取Topic和Group信息失败');
      }
    } catch (error) {
      message.error('连接失败，请检查配置是否正确');
      console.error(error);
      performanceMonitor.endMeasure('fetch_topics_groups');
    } finally {
      setTopicsLoading(false);
    }
  };

  // 创建或更新Topic配置
  const handleConfigSubmit = async (values) => {
    try {
      const url = currentConfig 
        ? '/api/v1/topic/config' 
        : '/api/v1/topic/config';
      const method = currentConfig ? 'PUT' : 'POST';
      
      const data = currentConfig 
        ? { ...values, id: currentConfig.id }
        : values;

      const res = await request(url, {
        method,
        data,
      });

      if (res.result === 'SUCCESS') {
        message.success(currentConfig ? '更新成功' : '创建成功');
        setConfigModalVisible(false);
        configForm.resetFields();
        setCurrentConfig(null);
        fetchConfigList();
      } else {
        message.error(currentConfig ? '更新失败' : '创建失败');
      }
    } catch (error) {
      message.error('操作失败');
      console.error(error);
    }
  };

  // 删除Topic配置
  const handleDeleteConfig = async (id) => {
    try {
      const res = await request(`/api/v1/topic/config/${id}`, {
        method: 'DELETE',
      });

      if (res.result === 'SUCCESS') {
        message.success('删除成功');
        fetchConfigList();
        // 清除对应的topics和groups缓存
        setTopicsAndGroups(prev => {
          const newData = { ...prev };
          delete newData[id];
          return newData;
        });
      } else {
        message.error('删除失败');
      }
    } catch (error) {
      message.error('删除失败');
      console.error(error);
    }
  };

  // 查看Topic和Group信息
  const handleViewTopics = (config) => {
    setCurrentTopicConfig(config);
    setTopicModalVisible(true);
    if (!topicsAndGroups[config.id]) {
      fetchTopicsAndGroups(config.id);
    }
  };

  // 刷新Topic和Group信息（强制从源获取，带性能监控）
  const handleRefreshTopics = async () => {
    if (!currentTopicConfig) return;

    performanceMonitor.startMeasure('refresh_topics_groups');
    setTopicsLoading(true);
    try {
      const res = await request(`/api/v1/topic/config/${currentTopicConfig.id}/topics-groups/refresh`, {
        method: 'POST',
      });
      if (res.result === 'SUCCESS') {
        // 监控数据处理性能
        performanceMonitor.measureDataProcessing('process_refresh_data', () => {
          setTopicsAndGroups(prev => ({
            ...prev,
            [currentTopicConfig.id]: res.data
          }));
        });

        const duration = performanceMonitor.endMeasure('refresh_topics_groups');
        message.success(`数据刷新成功 (${duration?.toFixed(2)}ms)`);
      } else {
        message.error(res.message || '刷新数据失败');
      }
    } catch (error) {
      message.error('刷新失败，请检查配置是否正确');
      console.error(error);
      performanceMonitor.endMeasure('refresh_topics_groups');
    } finally {
      setTopicsLoading(false);
    }
  };

  // 测试连接
  const handleTestConnection = async () => {
    try {
      await configForm.validateFields();
      const values = configForm.getFieldsValue();

      setTestingConnection(true);
      const res = await request('/api/v1/topic/config/test-connection', {
        method: 'POST',
        data: values,
      });

      if (res.result === 'SUCCESS') {
        message.success('连接测试成功！');
      } else {
        message.error(res.message || '连接测试失败');
      }
    } catch (error) {
      if (error.errorFields) {
        message.error('请先填写完整的配置信息');
      } else {
        message.error('连接测试失败，请检查配置');
        console.error(error);
      }
    } finally {
      setTestingConnection(false);
    }
  };

  // Topic配置表格列定义
  const configColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '配置名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 150,
      render: (type) => (
        <Tag
          color={type === 'kafka' ? 'blue' : 'orange'}
          size="small"
          style={{
            borderRadius: '8px',
            padding: '1px 8px',
            fontWeight: '500',
            border: 'none'
          }}
          icon={type === 'kafka' ? <ApiOutlined /> : <ClusterOutlined />}
        >
          {type === 'kafka' ? 'Kafka' : 'RocketMQ'}
        </Tag>
      ),
    },
    {
      title: '环境',
      dataIndex: ['envInfo', 'name'],
      key: 'env',
      width: 100,
      render: (envName) => (
        <Tag color="green" size="small" style={{ borderRadius: '6px' }}>
          {envName || '未知环境'}
        </Tag>
      ),
    },
    {
      title: '连接地址',
      dataIndex: 'address',
      key: 'address',
      ellipsis: true,
    },
    // {
    //   title: '用户名',
    //   dataIndex: 'username',
    //   key: 'username',
    //   render: (username) => username || '-',
    // },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size={4} className="action-buttons">
          <Tooltip title="查看Topics">
            <Button
              type="primary"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewTopics(record)}
              className="primary-gradient"
            >
              查看Topics
            </Button>
          </Tooltip>
          <Tooltip title="编辑配置">
            <Button
              size="small"
              icon={<EditOutlined />}
              onClick={() => {
                setCurrentConfig(record);
                configForm.setFieldsValue(record);
                setConfigModalVisible(true);
              }}
              className="edit-btn"
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个配置吗？"
            description="删除后将无法恢复，请谨慎操作"
            onConfirm={() => handleDeleteConfig(record.id)}
            okText="确定删除"
            cancelText="取消"
            okButtonProps={{ danger: true }}
          >
            <Tooltip title="删除配置">
              <Button
                size="small"
                danger
                icon={<DeleteOutlined />}
                className="delete-btn"
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 优化的Topic表格列定义 - 使用useMemo避免重复创建
  const topicColumns = useMemo(() => [
    {
      title: (
        <Space>
          <DatabaseOutlined style={{ color: '#1890ff' }} />
          <span>Topic名称</span>
        </Space>
      ),
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (name) => (
        <span style={{ color: '#262626', fontWeight: 500 }}>{name}</span>
      ),
    },
    {
      title: '分区数',
      dataIndex: 'partitions',
      key: 'partitions',
      align: 'center',
      width: 80,
      render: (partitions) => (
        <span style={{
          color: '#52c41a',
          fontWeight: 600,
          padding: '2px 8px',
          backgroundColor: '#f6ffed',
          borderRadius: '4px',
          fontSize: '12px'
        }}>
          {partitions || 0}
        </span>
      ),
    },
    {
      title: '副本数',
      dataIndex: 'replicas',
      key: 'replicas',
      align: 'center',
      width: 80,
      render: (replicas) => (
        <span style={{
          color: '#1890ff',
          fontWeight: 600,
          padding: '2px 8px',
          backgroundColor: '#f0f8ff',
          borderRadius: '4px',
          fontSize: '12px'
        }}>
          {replicas || 0}
        </span>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (description) => (
        <span style={{ color: '#8c8c8c' }}>{description || '暂无描述'}</span>
      ),
    },
  ], []);

  // 优化的Group表格列定义 - 使用useMemo避免重复创建
  const groupColumns = useMemo(() => [
    {
      title: (
        <Space>
          <TeamOutlined style={{ color: '#f5222d' }} />
          <span>Group名称</span>
        </Space>
      ),
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (name) => (
        <span style={{ color: '#262626', fontWeight: 500 }}>{name}</span>
      ),
    },
    {
      title: '成员数',
      dataIndex: 'members',
      key: 'members',
      align: 'center',
      width: 80,
      render: (members) => (
        <span style={{
          color: '#722ed1',
          fontWeight: 600,
          padding: '2px 8px',
          backgroundColor: '#f9f0ff',
          borderRadius: '4px',
          fontSize: '12px'
        }}>
          {members || 0}
        </span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'state',
      key: 'state',
      width: 80,
      render: (state) => (
        <span style={{ color: '#8c8c8c', fontSize: '12px' }}>{state || '-'}</span>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (description) => (
        <span style={{ color: '#8c8c8c' }}>{description || '暂无描述'}</span>
      ),
    },
  ], []);

  // 使用useMemo缓存计算结果，避免重复计算
  const currentTopicsAndGroups = useMemo(() => {
    return currentTopicConfig ? topicsAndGroups[currentTopicConfig.id] : null;
  }, [currentTopicConfig, topicsAndGroups]);

  const topicsCount = useMemo(() => {
    return currentTopicsAndGroups?.topics?.length || 0;
  }, [currentTopicsAndGroups]);

  const groupsCount = useMemo(() => {
    return currentTopicsAndGroups?.groups?.length || 0;
  }, [currentTopicsAndGroups]);



  return (
    <div className="topic-manager" style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      {/* 页面标题 */}
      <div className="page-header">
        <Title level={2} className="title">
          <DatabaseOutlined style={{ marginRight: '12px' }} />
          Topic 管理
        </Title>
        <div className="description">
          管理 Kafka 和 RocketMQ 的连接配置，查看 Topic 和 Group 信息
        </div>
      </div>

      <Card className="main-card">
        <Tabs
          activeKey={activeKey}
          onChange={setActiveKey}
          size="large"
          tabBarExtraContent={
            activeKey === '1' ? (
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setCurrentConfig(null);
                  configForm.resetFields();
                  setConfigModalVisible(true);
                }}
                style={{
                  borderRadius: '8px',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  border: 'none',
                  height: '40px',
                  padding: '0 20px',
                  fontWeight: '500'
                }}
              >
                添加配置
              </Button>
            ) : null
          }
        >
          <TabPane
            tab={
              <Space>
                <DatabaseOutlined />
                <span>Topic配置管理</span>
              </Space>
            }
            key="1"
          >
            <Table
              rowKey="id"
              columns={configColumns}
              dataSource={configList}
              loading={loading}
              className="config-table"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条配置`
              }}
              style={{ marginTop: '16px' }}
            />
          </TabPane>

          <TabPane
            tab={
              <Space>
                <SyncOutlined />
                <span>集群同步</span>
              </Space>
            }
            key="2"
          >
            <SyncManager />
          </TabPane>
        </Tabs>
      </Card>

      {/* Topic配置模态框 */}
      <Modal
        title={currentConfig ? '编辑Topic配置' : '添加Topic配置'}
        open={configModalVisible}
        onCancel={() => {
          setConfigModalVisible(false);
          configForm.resetFields();
          setCurrentConfig(null);
        }}
        footer={null}
        width={600}
      >
        <Form
          form={configForm}
          layout="vertical"
          onFinish={handleConfigSubmit}
        >
          <Form.Item
            name="env"
            label="环境"
            rules={[{ required: true, message: '请选择环境' }]}
          >
            <Select placeholder="选择环境">
              {envList.map(env => (
                <Option key={env.id} value={env.id}>
                  {env.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="name"
            label="配置名称"
            rules={[{ required: true, message: '请输入配置名称' }]}
          >
            <Input placeholder="配置名称" />
          </Form.Item>
          <Form.Item
            name="type"
            label="类型"
            rules={[{ required: true, message: '请选择类型' }]}
          >
            <Select placeholder="选择类型">
              <Option value="kafka">Kafka</Option>
              <Option value="rocketmq">RocketMQ</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="address"
            label="连接地址"
            rules={[{ required: true, message: '请输入连接地址' }]}
            extra={
              configForm.getFieldValue('type') === 'kafka'
                ? "Kafka地址格式: host:port 或 host1:port1,host2:port2"
                : "RocketMQ NameServer地址格式: host:port"
            }
          >
            <Input placeholder={
              configForm.getFieldValue('type') === 'kafka'
                ? "例如: localhost:9092"
                : "例如: localhost:9876"
            } />
          </Form.Item>
          <Form.Item
            name="username"
            label="用户名"
          >
            <Input placeholder="用户名（可选）" />
          </Form.Item>
          <Form.Item
            name="password"
            label="密码"
          >
            <Input.Password placeholder="密码（可选）" />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={3} placeholder="描述信息" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {currentConfig ? '更新' : '创建'}
              </Button>
              <Button
                type="default"
                onClick={handleTestConnection}
                loading={testingConnection}
              >
                测试连接
              </Button>
              <Button onClick={() => {
                setConfigModalVisible(false);
                configForm.resetFields();
                setCurrentConfig(null);
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Topic和Group查看模态框 */}
      <Modal
        className="topic-modal"
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Space>
              <DatabaseOutlined style={{ color: '#1890ff' }} />
              <Title level={4} style={{ margin: 0 }}>Topic 和 Group 信息</Title>
              <Tag color="blue" style={{ marginLeft: 8 }}>
                {currentTopicConfig?.type === 'kafka' ? 'Kafka' : 'RocketMQ'}
              </Tag>
            </Space>
            <Space>
              <Tooltip title="从缓存获取数据，速度更快">
                <Button
                  size="small"
                  icon={<EyeOutlined />}
                  onClick={() => fetchTopicsAndGroups(currentTopicConfig.id)}
                  loading={topicsLoading}
                  style={{ borderRadius: '6px' }}
                >
                  查看缓存
                </Button>
              </Tooltip>
              <Tooltip title="从消息队列实时获取最新数据并更新缓存">
                <Button
                  type="primary"
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={handleRefreshTopics}
                  loading={topicsLoading}
                  style={{ borderRadius: '6px' }}
                >
                  刷新数据
                </Button>
              </Tooltip>
            </Space>
          </div>
        }
        open={topicModalVisible}
        onCancel={() => {
          setTopicModalVisible(false);
          setCurrentTopicConfig(null);
        }}
        footer={null}
        width={1200}
        style={{ top: 20 }}
        bodyStyle={{ padding: '24px' }}
      >
        {currentTopicConfig && (
          <>
            <Card className="config-info-card">
              <Row align="middle" gutter={24}>
                <Col>
                  <Avatar
                    size={48}
                    style={{
                      backgroundColor: currentTopicConfig.type === 'kafka' ? '#1890ff' : '#fa8c16',
                      fontSize: '20px'
                    }}
                  >
                    {currentTopicConfig.type === 'kafka' ? <ApiOutlined /> : <ClusterOutlined />}
                  </Avatar>
                </Col>
                <Col flex={1}>
                  <div>
                    <Title level={4} style={{ margin: 0, color: '#262626' }}>
                      {currentTopicConfig.name}
                    </Title>
                    <Space size="middle" style={{ marginTop: 8 }}>
                      <Tag
                        color={currentTopicConfig.type === 'kafka' ? 'blue' : 'orange'}
                        style={{ borderRadius: '12px', padding: '2px 12px' }}
                      >
                        {currentTopicConfig.type === 'kafka' ? 'Kafka' : 'RocketMQ'}
                      </Tag>
                      <Text type="secondary">
                        <DatabaseOutlined style={{ marginRight: 4 }} />
                        {currentTopicConfig.address}
                      </Text>
                    </Space>
                  </div>
                </Col>
                <Col>
                  <Tooltip title="连接状态">
                    <CheckCircleOutlined style={{ fontSize: '20px', color: '#52c41a' }} />
                  </Tooltip>
                </Col>
              </Row>
            </Card>
            
            {topicsLoading ? (
              <div style={{ textAlign: 'center', padding: '80px 20px' }}>
                <Spin size="large" />
                <div style={{ marginTop: 16 }}>
                  <Text type="secondary">正在连接并获取数据...</Text>
                </div>
              </div>
            ) : currentTopicsAndGroups ? (
              <>
                {/* 统计信息卡片 - 使用缓存的计算结果 */}
                <Row gutter={16} style={{ marginBottom: 16 }}>
                  <Col span={8}>
                    <Card size="small" className="stats-card">
                      <Statistic
                        title="Topic 数量"
                        value={topicsCount}
                        prefix={<DatabaseOutlined style={{ color: '#1890ff' }} />}
                        valueStyle={{ color: '#1890ff' }}
                      />
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card size="small" className="stats-card">
                      <Statistic
                        title={currentTopicConfig.type === 'rocketmq' ? '订阅组数量' : '消费组数量'}
                        value={groupsCount}
                        prefix={<TeamOutlined style={{ color: '#f5222d' }} />}
                        valueStyle={{ color: '#f5222d' }}
                      />
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card size="small" className="stats-card">
                      <Statistic
                        title="连接状态"
                        value="正常"
                        prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                        valueStyle={{ color: '#52c41a' }}
                      />
                    </Card>
                  </Col>
                </Row>

                {/* 数据表格 */}
                <Row gutter={16}>
                  <Col span={12}>
                    <Card
                      title={
                        <Space>
                          <Avatar size="small" style={{ backgroundColor: '#1890ff' }}>
                            <DatabaseOutlined />
                          </Avatar>
                          <span>Topics</span>
                          <Badge
                            count={topicsCount}
                            style={{ backgroundColor: '#52c41a' }}
                            overflowCount={999999}
                            showZero
                          />
                        </Space>
                      }
                      size="small"
                      className="data-card"
                    >
                      <PerformantTable
                        rowKey="name"
                        columns={topicColumns}
                        dataSource={currentTopicsAndGroups?.topics || []}
                        loading={topicsLoading}
                        searchable={true}
                        searchPlaceholder="搜索Topic名称..."
                        defaultPageSize={50}
                      />
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card
                      title={
                        <Space>
                          <Avatar size="small" style={{ backgroundColor: '#f5222d' }}>
                            <TeamOutlined />
                          </Avatar>
                          <span>{currentTopicConfig.type === 'rocketmq' ? 'Subscription Groups' : 'Consumer Groups'}</span>
                          <Badge
                            count={groupsCount}
                            style={{ backgroundColor: '#52c41a' }}
                            overflowCount={999999}
                            showZero
                          />
                        </Space>
                      }
                      size="small"
                      className="data-card"
                    >
                      <PerformantTable
                        rowKey="name"
                        columns={groupColumns}
                        dataSource={currentTopicsAndGroups?.groups || []}
                        loading={topicsLoading}
                        searchable={true}
                        searchPlaceholder={`搜索${currentTopicConfig.type === 'rocketmq' ? '订阅组' : '消费组'}名称...`}
                        defaultPageSize={50}
                      />
                    </Card>
                  </Col>
                </Row>
              </>
            ) : (
              <div style={{ textAlign: 'center', padding: '80px 20px' }}>
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description={
                    <div>
                      <Text type="secondary">暂无数据</Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: '12px' }}>请点击刷新按钮获取最新数据</Text>
                    </div>
                  }
                />
              </div>
            )}
          </>
        )}
      </Modal>
    </div>
  );
};

export default TopicManager;
