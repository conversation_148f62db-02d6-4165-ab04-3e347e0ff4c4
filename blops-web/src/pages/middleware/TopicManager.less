.topic-manager {
  .page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 24px;
    border-radius: 12px;
    margin-bottom: 24px;
    color: white;
    
    .title {
      color: white !important;
      margin: 0;
    }
    
    .description {
      color: rgba(255, 255, 255, 0.85);
      margin-top: 8px;
    }
  }
  
  .main-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: none;
    
    .ant-tabs-tab {
      font-weight: 500;
      
      &.ant-tabs-tab-active {
        .ant-tabs-tab-btn {
          color: #1890ff;
          font-weight: 600;
        }
      }
    }
  }
  
  .config-table {
    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 600;
      color: #262626;
    }
    
    .ant-table-tbody > tr:hover > td {
      background: #f5f5f5;
    }
  }
  
  .topic-modal {
    .ant-modal-header {
      border-radius: 8px 8px 0 0;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    }
    
    .config-info-card {
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      border: none;
      border-radius: 8px;
      margin-bottom: 24px;
    }
    
    .stats-card {
      text-align: center;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }

      .ant-statistic-title {
        font-size: 12px;
        color: #8c8c8c;
        margin-bottom: 4px;
      }

      .ant-statistic-content {
        font-size: 20px;
        font-weight: 600;
      }

      &.topics-stats {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      &.groups-stats {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
      }
    }
    
    .data-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      .ant-card-head {
        border-bottom: 1px solid #f0f0f0;
        
        .ant-card-head-title {
          padding: 16px 0;
        }
      }
      
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
      }
    }
  }
  
  .action-buttons {
    .ant-btn {
      border-radius: 6px;
      font-weight: 500;
      
      &.primary-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        
        &:hover {
          background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
      }
      
      &.edit-btn {
        border-color: #52c41a;
        color: #52c41a;
        
        &:hover {
          background: #52c41a;
          color: white;
          transform: translateY(-1px);
        }
      }
      
      &.delete-btn {
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(255, 77, 79, 0.4);
        }
      }
    }
  }
  
  .type-tag {
    border-radius: 12px;
    padding: 2px 12px;
    font-weight: 500;
    border: none;
    
    &.kafka-tag {
      background: linear-gradient(135deg, #1890ff, #096dd9);
      color: white;
    }
    
    &.rocketmq-tag {
      background: linear-gradient(135deg, #fa8c16, #d46b08);
      color: white;
    }
  }
  
  .status-tag {
    border-radius: 12px;
    padding: 2px 8px;
    font-weight: 500;
    
    &.online {
      background: linear-gradient(135deg, #52c41a, #389e0d);
      color: white;
    }
    
    &.offline {
      background: linear-gradient(135deg, #ff4d4f, #cf1322);
      color: white;
    }
    
    &.unknown {
      background: linear-gradient(135deg, #faad14, #d48806);
      color: white;
    }
  }
  
  .loading-container {
    text-align: center;
    padding: 80px 20px;
    
    .ant-spin-dot {
      font-size: 24px;
    }
    
    .loading-text {
      margin-top: 16px;
      color: #8c8c8c;
      font-size: 14px;
    }
  }
  
  .empty-container {
    text-align: center;
    padding: 80px 20px;
    
    .ant-empty-description {
      color: #8c8c8c;
    }
  }
  
  .badge-count {
    &.success {
      background-color: #52c41a;
    }
    
    &.primary {
      background-color: #1890ff;
    }
    
    &.purple {
      background-color: #722ed1;
    }
  }
}

/* 全局样式优化 */
.ant-table-small .ant-table-tbody > tr > td {
  padding: 8px 8px;
}

.ant-modal-header {
  border-radius: 8px 8px 0 0;
}

.ant-modal-content {
  border-radius: 8px;
  overflow: hidden;
}

.ant-card {
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  }
}

.ant-btn {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-1px);
  }
}
