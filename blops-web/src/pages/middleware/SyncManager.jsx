import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Table,
  Space,
  Tag,
  Progress,
  Modal,
  Form,
  Select,
  Switch,
  Input,
  message,
  Tooltip,
  Popconfirm,
  Badge,
  Descriptions,
  Tabs,
  List,
  Typography,
  Row,
  Col,
  Statistic,
  Divider
} from 'antd';
import {
  PlusOutlined,
  SyncOutlined,
  DeleteOutlined,
  EyeOutlined,
  PlayCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { request } from 'umi';
import './SyncManager.less';

const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;
const { Text, Title } = Typography;

const SyncManager = () => {
  const [loading, setLoading] = useState(false);
  const [syncTasks, setSyncTasks] = useState([]);
  const [topicConfigs, setTopicConfigs] = useState([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [previewData, setPreviewData] = useState(null);
  const [selectedTask, setSelectedTask] = useState(null);
  const [form] = Form.useForm();

  // 获取同步任务列表
  const fetchSyncTasks = async () => {
    setLoading(true);
    try {
      const response = await request('/api/v1/sync/task');
      if (response.result === 'SUCCESS') {
        setSyncTasks(response.data || []);
      }
    } catch (error) {
      message.error('获取同步任务列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取Topic配置列表
  const fetchTopicConfigs = async () => {
    try {
      const response = await request('/api/v1/topic/config?with_env_info=true');
      if (response.result === 'SUCCESS') {
        setTopicConfigs(response.data || []);
      }
    } catch (error) {
      message.error('获取配置列表失败');
    }
  };

  useEffect(() => {
    fetchSyncTasks();
    fetchTopicConfigs();
  }, []);

  // 状态标签渲染
  const renderStatusTag = (status) => {
    const statusConfig = {
      pending: { color: 'default', text: '待执行' },
      running: { color: 'processing', text: '执行中' },
      completed: { color: 'success', text: '已完成' },
      failed: { color: 'error', text: '失败' },
      cancelled: { color: 'warning', text: '已取消' }
    };
    const config = statusConfig[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 进度条渲染
  const renderProgress = (record) => {
    const { progress, status } = record;
    let strokeColor = '#1890ff';
    if (status === 'completed') strokeColor = '#52c41a';
    if (status === 'failed') strokeColor = '#ff4d4f';
    if (status === 'cancelled') strokeColor = '#faad14';

    return (
      <Progress
        percent={progress}
        size="small"
        strokeColor={strokeColor}
        showInfo={progress > 0}
      />
    );
  };

  // 同步预览
  const handlePreview = async (values) => {
    try {
      const response = await request('/api/v1/sync/preview', {
        method: 'POST',
        data: values
      });
      if (response.result === 'SUCCESS') {
        setPreviewData(response.data);
        setPreviewModalVisible(true);
      } else {
        message.error(response.message || '预览失败');
      }
    } catch (error) {
      message.error('预览失败');
    }
  };

  // 创建同步任务
  const handleCreate = async (values) => {
    try {
      const response = await request('/api/v1/sync/task', {
        method: 'POST',
        data: values
      });
      if (response.result === 'SUCCESS') {
        message.success('同步任务创建成功');
        setCreateModalVisible(false);
        form.resetFields();
        fetchSyncTasks();
      } else {
        message.error(response.message || '创建失败');
      }
    } catch (error) {
      message.error('创建失败');
    }
  };

  // 执行同步任务
  const handleExecute = async (taskId) => {
    try {
      const response = await request(`/api/v1/sync/task/${taskId}/execute`, {
        method: 'POST'
      });
      if (response.result === 'SUCCESS') {
        message.success('同步任务已开始执行');
        fetchSyncTasks();
      } else {
        message.error(response.message || '执行失败');
      }
    } catch (error) {
      message.error('执行失败');
    }
  };

  // 取消同步任务
  const handleCancel = async (taskId) => {
    try {
      const response = await request(`/api/v1/sync/task/${taskId}/cancel`, {
        method: 'POST'
      });
      if (response.result === 'SUCCESS') {
        message.success('同步任务已取消');
        fetchSyncTasks();
      } else {
        message.error(response.message || '取消失败');
      }
    } catch (error) {
      message.error('取消失败');
    }
  };

  // 删除同步任务
  const handleDelete = async (taskId) => {
    try {
      const response = await request(`/api/v1/sync/task/${taskId}`, {
        method: 'DELETE'
      });
      if (response.result === 'SUCCESS') {
        message.success('同步任务已删除');
        fetchSyncTasks();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 查看任务详情
  const handleViewDetail = async (task) => {
    setSelectedTask(task);
    setDetailModalVisible(true);
  };

  // 表格列定义
  const columns = [
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      ellipsis: true
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 80,
      render: (type) => (
        <Tag color={type === 'kafka' ? 'blue' : 'orange'} size="small">
          {type?.toUpperCase()}
        </Tag>
      )
    },
    {
      title: '源配置',
      dataIndex: 'sourceConfig',
      key: 'sourceConfig',
      width: 140,
      ellipsis: true,
      render: (config) => config?.name || '-'
    },
    {
      title: '目标配置',
      dataIndex: 'targetConfig',
      key: 'targetConfig',
      width: 140,
      ellipsis: true,
      render: (config) => config?.name || '-'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 90,
      render: renderStatusTag
    },
    {
      title: '进度',
      key: 'progress',
      width: 100,
      render: (_, record) => renderProgress(record)
    },
    {
      title: '统计',
      key: 'stats',
      width: 110,
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Text type="secondary" style={{ fontSize: '11px' }}>
            总计: {record.totalItems || 0}
          </Text>
          <Space size={4}>
            <Badge count={record.completedItems || 0} color="green" size="small" />
            <Badge count={record.skippedItems || 0} color="orange" size="small" />
            <Badge count={record.failedItems || 0} color="red" size="small" />
          </Space>
        </Space>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 140,
      render: (time) => time ? new Date(time).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 140,
      fixed: 'right',
      render: (_, record) => (
        <Space size={4}>
          <Tooltip title="查看详情">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          {record.status === 'pending' && (
            <Tooltip title="执行任务">
              <Button
                type="text"
                size="small"
                icon={<PlayCircleOutlined />}
                onClick={() => handleExecute(record.id)}
                style={{ color: '#52c41a' }}
              />
            </Tooltip>
          )}
          {record.status === 'running' && (
            <Tooltip title="取消任务">
              <Button
                type="text"
                size="small"
                icon={<StopOutlined />}
                onClick={() => handleCancel(record.id)}
                style={{ color: '#faad14' }}
              />
            </Tooltip>
          )}
          <Popconfirm
            title="确定要删除这个同步任务吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除任务">
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div className="sync-manager">
      <Card
        title={
          <Space>
            <SyncOutlined />
            <span>MQ集群同步管理</span>
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchSyncTasks}
              loading={loading}
            >
              刷新
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              创建同步任务
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={syncTasks}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1000 }}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 创建同步任务模态框 */}
      <CreateSyncTaskModal
        visible={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false);
          form.resetFields();
        }}
        onPreview={handlePreview}
        onCreate={handleCreate}
        topicConfigs={topicConfigs}
        form={form}
      />

      {/* 同步预览模态框 */}
      <PreviewModal
        visible={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        data={previewData}
        onCreate={handleCreate}
        form={form}
        topicConfigs={topicConfigs}
      />

      {/* 任务详情模态框 */}
      <TaskDetailModal
        visible={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        task={selectedTask}
        renderStatusTag={renderStatusTag}
        renderProgress={renderProgress}
      />
    </div>
  );
};

// 创建同步任务模态框
const CreateSyncTaskModal = ({ visible, onCancel, onPreview, onCreate, topicConfigs, form }) => {
  const [previewLoading, setPreviewLoading] = useState(false);

  const handlePreview = async () => {
    try {
      const values = await form.validateFields();
      setPreviewLoading(true);
      await onPreview(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setPreviewLoading(false);
    }
  };

  const getConfigsByType = (type) => {
    return topicConfigs.filter(config => config.type === type);
  };

  const selectedType = Form.useWatch('type', form);
  const availableConfigs = selectedType ? getConfigsByType(selectedType) : [];

  return (
    <Modal
      title="创建同步任务"
      open={visible}
      onCancel={onCancel}
      width={600}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="preview"
          type="default"
          loading={previewLoading}
          onClick={handlePreview}
        >
          预览差异
        </Button>
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          syncTopics: true,
          syncGroups: true,
          skipExisting: true
        }}
      >
        <Form.Item
          name="name"
          label="任务名称"
          rules={[{ required: true, message: '请输入任务名称' }]}
        >
          <Input placeholder="请输入任务名称" />
        </Form.Item>

        <Form.Item
          name="type"
          label="同步类型"
          rules={[{ required: true, message: '请选择同步类型' }]}
        >
          <Select placeholder="请选择同步类型">
            <Option value="kafka">Kafka</Option>
            <Option value="rocketmq">RocketMQ</Option>
          </Select>
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="sourceConfigId"
              label="源配置"
              rules={[{ required: true, message: '请选择源配置' }]}
            >
              <Select placeholder="请选择源配置" disabled={!selectedType}>
                {availableConfigs.map(config => (
                  <Option key={config.id} value={config.id}>
                    {config.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="targetConfigId"
              label="目标配置"
              rules={[{ required: true, message: '请选择目标配置' }]}
            >
              <Select placeholder="请选择目标配置" disabled={!selectedType}>
                {availableConfigs.map(config => (
                  <Option key={config.id} value={config.id}>
                    {config.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item name="syncTopics" valuePropName="checked">
              <Switch checkedChildren="同步Topic" unCheckedChildren="跳过Topic" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="syncGroups" valuePropName="checked">
              <Switch checkedChildren="同步Group" unCheckedChildren="跳过Group" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="skipExisting" valuePropName="checked">
              <Switch checkedChildren="跳过已存在" unCheckedChildren="强制覆盖" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item>
          <div style={{
            padding: '12px',
            background: '#f6f8fa',
            borderRadius: '6px',
            fontSize: '12px',
            color: '#666'
          }}>
            <InfoCircleOutlined style={{ marginRight: '8px' }} />
            提示：点击"预览差异"可以查看将要同步的内容，确认无误后再创建任务
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};

// 同步预览模态框
const PreviewModal = ({ visible, onCancel, data, onCreate, form, topicConfigs }) => {
  const [createLoading, setCreateLoading] = useState(false);

  const handleCreate = async () => {
    try {
      const values = await form.validateFields();
      setCreateLoading(true);
      await onCreate(values);
      onCancel();
    } catch (error) {
      console.error('创建失败:', error);
    } finally {
      setCreateLoading(false);
    }
  };

  if (!data) return null;

  // 适配后端返回的数据结构
  const sourceInfo = data.sourceConfig;
  const targetInfo = data.targetConfig;

  // 从topicConfigs中获取目标集群的真实统计数据
  const getTargetClusterStats = () => {
    if (!targetInfo?.id || !topicConfigs) return { topicCount: null, groupCount: null };

    const targetConfig = topicConfigs.find(config => config.id === targetInfo.id);
    if (!targetConfig) return { topicCount: null, groupCount: null };

    // 如果配置中有统计数据，使用它们
    return {
      topicCount: targetConfig.topicCount || null,
      groupCount: targetConfig.groupCount || null
    };
  };

  const targetStats = getTargetClusterStats();
  const diff = {
    topics: data.topicsToSync || [],
    groups: data.groupsToSync || []
  };
  const skipInfo = {
    topics: data.topicsToSkip || [],
    groups: data.groupsToSkip || []
  };

  const renderDiffList = (items, type) => {
    if (!items || items.length === 0) {
      return <div style={{ textAlign: 'center', color: '#999' }}>无{type}需要同步</div>;
    }

    return (
      <List
        size="small"
        dataSource={items}
        renderItem={(item) => (
          <List.Item>
            <Space>
              <CheckCircleOutlined style={{ color: '#52c41a' }} />
              <Text>{item.name}</Text>
              {item.partitions && (
                <Tag size="small">分区: {item.partitions}</Tag>
              )}
              {item.replicas && (
                <Tag size="small">副本: {item.replicas}</Tag>
              )}
              {item.queueNum && (
                <Tag size="small">队列: {item.queueNum}</Tag>
              )}
            </Space>
          </List.Item>
        )}
      />
    );
  };

  return (
    <Modal
      title="同步预览"
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={
        [
          <Button key="cancel" onClick={onCancel}>
            取消
          </Button>,
          <Button
            key="create"
            type="primary"
            loading={createLoading}
            onClick={handleCreate}
            disabled={!diff?.topics?.length && !diff?.groups?.length}
          >
            确认创建
          </Button>
        ]}
    >
      <div style={{ maxHeight: '600px', overflowY: 'auto' }}>
        {/* 集群信息 */}
        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col span={12}>
            <Card size="small" title="源集群信息">
              <Descriptions size="small" column={1}>
                <Descriptions.Item label="配置名称">{sourceInfo?.name}</Descriptions.Item>
                <Descriptions.Item label="类型">{sourceInfo?.type?.toUpperCase()}</Descriptions.Item>
                <Descriptions.Item label="地址">{sourceInfo?.address}</Descriptions.Item>
                <Descriptions.Item label="Topic数量">
                  {sourceInfo?.topicCount ?? (((diff.topics?.length || 0) + (skipInfo.topics?.length || 0)) || '-')}
                </Descriptions.Item>
                <Descriptions.Item label="Group数量">
                  {sourceInfo?.groupCount ?? (((diff.groups?.length || 0) + (skipInfo.groups?.length || 0)) || '-')}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>
          <Col span={12}>
            <Card size="small" title="目标集群信息">
              <Descriptions size="small" column={1}>
                <Descriptions.Item label="配置名称">{targetInfo?.name}</Descriptions.Item>
                <Descriptions.Item label="类型">{targetInfo?.type?.toUpperCase()}</Descriptions.Item>
                <Descriptions.Item label="地址">{targetInfo?.address}</Descriptions.Item>
                <Descriptions.Item label="Topic数量">
                  {targetInfo?.topicCount ?? targetStats?.topicCount ?? (skipInfo.topics?.length || '-')}
                </Descriptions.Item>
                <Descriptions.Item label="Group数量">
                  {targetInfo?.groupCount ?? targetStats?.groupCount ?? (skipInfo.groups?.length || '-')}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>
        </Row>

        {/* 同步差异 */}
        <Tabs defaultActiveKey="topics">
          <TabPane
            tab={
              <Space>
                <span>Topics</span>
                <Badge count={diff?.topics?.length || 0} color="blue" />
              </Space>
            }
            key="topics"
          >
            <Card size="small" title="将要同步的Topics">
              {renderDiffList(diff?.topics, 'Topic')}
            </Card>
            {skipInfo?.topics?.length > 0 && (
              <Card size="small" title="跳过的Topics" style={{ marginTop: '16px' }}>
                <div style={{ color: '#999', marginBottom: '8px' }}>
                  以下Topics在目标集群中已存在，将被跳过：
                </div>
                {renderDiffList(skipInfo?.topics, 'Topic')}
              </Card>
            )}
          </TabPane>
          <TabPane
            tab={
              <Space>
                <span>Groups</span>
                <Badge count={diff?.groups?.length || 0} color="orange" />
              </Space>
            }
            key="groups"
          >
            <Card size="small" title="将要同步的Groups">
              {renderDiffList(diff?.groups, 'Group')}
            </Card>
            {skipInfo?.groups?.length > 0 && (
              <Card size="small" title="跳过的Groups" style={{ marginTop: '16px' }}>
                <div style={{ color: '#999', marginBottom: '8px' }}>
                  以下Groups在目标集群中已存在，将被跳过：
                </div>
                {renderDiffList(skipInfo?.groups, 'Group')}
              </Card>
            )}
          </TabPane>
        </Tabs>

        {/* 统计信息 */}
        <Card size="small" title="同步统计" style={{ marginTop: '16px' }}>
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="Topics"
                value={diff?.topics?.length || 0}
                prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                suffix="个需要同步"
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="Groups"
                value={diff?.groups?.length || 0}
                prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                suffix="个需要同步"
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="跳过"
                value={(skipInfo?.topics?.length || 0) + (skipInfo?.groups?.length || 0)}
                prefix={<ExclamationCircleOutlined style={{ color: '#faad14' }} />}
                suffix="个已存在"
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="总计"
                value={data?.totalItems || 0}
                prefix={<SyncOutlined style={{ color: '#1890ff' }} />}
                suffix="个项目"
              />
            </Col>
          </Row>
        </Card>

        {(!diff?.topics?.length && !diff?.groups?.length) && (
          <div style={{
            textAlign: 'center',
            padding: '40px',
            color: '#999'
          }}>
            <ExclamationCircleOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
            <div>没有发现需要同步的内容</div>
            <div style={{ fontSize: '12px', marginTop: '8px' }}>
              可能目标集群已包含所有源集群的内容
            </div>
          </div>
        )}
      </div>
    </Modal >
  );
};

// 任务详情模态框
const TaskDetailModal = ({ visible, onCancel, task, renderStatusTag, renderProgress }) => {
  const [activeTab, setActiveTab] = useState('info');
  const [syncItems, setSyncItems] = useState([]);
  const [syncLogs, setSyncLogs] = useState([]);
  const [itemsLoading, setItemsLoading] = useState(false);
  const [logsLoading, setLogsLoading] = useState(false);

  // 获取同步项目列表
  const fetchSyncItems = async (taskId) => {
    if (!taskId) return;
    setItemsLoading(true);
    try {
      const response = await request(`/api/v1/sync/task/${taskId}/items`);
      if (response.result === 'SUCCESS') {
        setSyncItems(response.data || []);
      }
    } catch (error) {
      message.error('获取同步项目失败');
    } finally {
      setItemsLoading(false);
    }
  };

  // 获取同步日志列表
  const fetchSyncLogs = async (taskId) => {
    if (!taskId) return;
    setLogsLoading(true);
    try {
      const response = await request(`/api/v1/sync/task/${taskId}/logs`);
      if (response.result === 'SUCCESS') {
        setSyncLogs(response.data || []);
      }
    } catch (error) {
      message.error('获取同步日志失败');
    } finally {
      setLogsLoading(false);
    }
  };

  useEffect(() => {
    if (visible && task?.id) {
      fetchSyncItems(task.id);
      fetchSyncLogs(task.id);
    }
  }, [visible, task?.id]);

  if (!task) return null;

  const itemColumns = [
    {
      title: '类型',
      dataIndex: 'itemType',
      key: 'itemType',
      width: 50,
      render: (type) => (
        <Tag color={type === 'topic' ? 'blue' : 'orange'}>
          {type?.toUpperCase()}
        </Tag>
      )
    },
    {
      title: '名称',
      dataIndex: 'itemName',
      key: 'itemName',
      ellipsis: true
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => {
        const statusConfig = {
          pending: { color: 'default', text: '待处理' },
          completed: { color: 'success', text: '已完成' },
          skipped: { color: 'warning', text: '已跳过' },
          failed: { color: 'error', text: '失败' }
        };
        const config = statusConfig[status] || { color: 'default', text: status };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '错误信息',
      dataIndex: 'errorMsg',
      key: 'errorMsg',
      ellipsis: true,
      render: (msg) => msg || '-'
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 160,
      render: (time) => time ? new Date(time).toLocaleString() : '-'
    }
  ];

  const logColumns = [
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      width: 80,
      render: (level) => {
        const levelConfig = {
          info: { color: 'blue', icon: <InfoCircleOutlined /> },
          warn: { color: 'orange', icon: <ExclamationCircleOutlined /> },
          error: { color: 'red', icon: <CloseCircleOutlined /> }
        };
        const config = levelConfig[level] || { color: 'default', icon: null };
        return (
          <Tag color={config.color} icon={config.icon}>
            {level?.toUpperCase()}
          </Tag>
        );
      }
    },
    {
      title: '消息',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true
    },
    {
      title: '详细信息',
      dataIndex: 'details',
      key: 'details',
      ellipsis: true,
      render: (details) => details || '-'
    },
    {
      title: '时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (time) => time ? new Date(time).toLocaleString() : '-'
    }
  ];

  return (
    <Modal
      title={`任务详情 - ${task.name}`}
      open={visible}
      onCancel={onCancel}
      width={1000}
      footer={[
        <Button key="close" onClick={onCancel}>
          关闭
        </Button>
      ]}
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="基本信息" key="info">
          <Row gutter={16}>
            <Col span={12}>
              <Descriptions title="任务信息" column={1} bordered size="small">
                <Descriptions.Item label="任务名称">{task.name}</Descriptions.Item>
                <Descriptions.Item label="同步类型">
                  <Tag color={task.type === 'kafka' ? 'blue' : 'orange'}>
                    {task.type?.toUpperCase()}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="状态">
                  {renderStatusTag(task.status)}
                </Descriptions.Item>
                <Descriptions.Item label="进度">
                  {renderProgress(task)}
                </Descriptions.Item>
                <Descriptions.Item label="创建时间">
                  {task.createdAt ? new Date(task.createdAt).toLocaleString() : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="开始时间">
                  {task.startedAt ? new Date(task.startedAt).toLocaleString() : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="完成时间">
                  {task.completedAt ? new Date(task.completedAt).toLocaleString() : '-'}
                </Descriptions.Item>
              </Descriptions>
            </Col>
            <Col span={12}>
              <Descriptions title="配置信息" column={1} bordered size="small">
                <Descriptions.Item label="源配置">
                  {task.sourceConfig?.name || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="目标配置">
                  {task.targetConfig?.name || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="同步选项">
                  <Space>
                    {task.syncTopics && <Tag color="blue">Topic</Tag>}
                    {task.syncGroups && <Tag color="orange">Group</Tag>}
                    {task.skipExisting && <Tag color="green">跳过已存在</Tag>}
                  </Space>
                </Descriptions.Item>
              </Descriptions>

              <Card size="small" title="统计信息" style={{ marginTop: '16px' }}>
                <Row gutter={16}>
                  <Col span={12}>
                    <Statistic
                      title="总计"
                      value={task.totalItems || 0}
                      prefix={<SyncOutlined />}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="已完成"
                      value={task.completedItems || 0}
                      prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                    />
                  </Col>
                </Row>
                <Row gutter={16} style={{ marginTop: '16px' }}>
                  <Col span={12}>
                    <Statistic
                      title="已跳过"
                      value={task.skippedItems || 0}
                      prefix={<ExclamationCircleOutlined style={{ color: '#faad14' }} />}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="失败"
                      value={task.failedItems || 0}
                      prefix={<CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
                    />
                  </Col>
                </Row>
              </Card>
            </Col>
          </Row>

          {task.errorMessage && (
            <Card size="small" title="错误信息" style={{ marginTop: '16px' }}>
              <Text type="danger">{task.errorMessage}</Text>
            </Card>
          )}
        </TabPane>

        <TabPane
          tab={
            <Space>
              <span>同步项目</span>
              <Badge count={syncItems.length} color="blue" />
            </Space>
          }
          key="items"
        >
          <Table
            columns={itemColumns}
            dataSource={syncItems}
            rowKey="id"
            loading={itemsLoading}
            size="small"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showTotal: (total) => `共 ${total} 条记录`
            }}
          />
        </TabPane>

        <TabPane
          tab={
            <Space>
              <span>执行日志</span>
              <Badge count={syncLogs.length} color="green" />
            </Space>
          }
          key="logs"
        >
          <Table
            columns={logColumns}
            dataSource={syncLogs}
            rowKey="id"
            loading={logsLoading}
            size="small"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showTotal: (total) => `共 ${total} 条记录`
            }}
          />
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default SyncManager;
