.virtualized-table-container {
  position: relative;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  overflow: hidden;

  .virtual-table-header {
    position: sticky;
    top: 0;
    z-index: 2;
    background: #fafafa;
    border-bottom: 2px solid #f0f0f0;
    
    .virtual-table-header-cell {
      font-weight: 600;
      color: #262626;
      font-size: 14px;
      
      &:last-child {
        border-right: none;
      }
    }
  }

  .virtual-table-row {
    background: #fff;
    transition: background-color 0.2s;
    
    &:hover {
      background: #f5f5f5;
    }
    
    &:nth-child(even) {
      background: #fafafa;
      
      &:hover {
        background: #f0f0f0;
      }
    }
    
    .virtual-table-cell {
      font-size: 13px;
      color: #595959;
      line-height: 1.4;
      
      &:last-child {
        border-right: none;
      }
    }
  }
}

// 优化滚动条样式
.virtualized-table-container ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.virtualized-table-container ::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.virtualized-table-container ::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  
  &:hover {
    background: #a8a8a8;
  }
}
