import React, { useMemo, useCallback } from 'react';
import { Table, Empty } from 'antd';
import { FixedSizeList as List } from 'react-window';

// 虚拟化表格组件 - 用于处理大数据集
const VirtualizedTable = ({
  columns,
  dataSource,
  rowKey,
  height = 400,
  rowHeight = 32,
  loading = false,
  pagination = false,
  emptyText = '暂无数据',
  ...tableProps
}) => {
  // 计算可见行数
  const visibleRowCount = Math.ceil(height / rowHeight);
  
  // 使用useMemo缓存处理后的数据
  const processedData = useMemo(() => {
    if (!dataSource || !Array.isArray(dataSource)) return [];
    return dataSource.map((item, index) => ({
      ...item,
      _index: index,
      _key: typeof rowKey === 'function' ? rowKey(item, index) : item[rowKey] || index
    }));
  }, [dataSource, rowKey]);

  // 渲染单行的组件
  const RowRenderer = useCallback(({ index, style }) => {
    const record = processedData[index];
    if (!record) return null;

    return (
      <div style={style} className="virtual-table-row">
        {columns.map((column, colIndex) => {
          const { dataIndex, render, width = 100, align = 'left' } = column;
          let cellContent = record[dataIndex];
          
          if (render && typeof render === 'function') {
            cellContent = render(cellContent, record, index);
          }

          return (
            <div
              key={colIndex}
              className="virtual-table-cell"
              style={{
                width: width,
                textAlign: align,
                padding: '8px 12px',
                borderBottom: '1px solid #f0f0f0',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                display: 'inline-block',
                verticalAlign: 'top',
                boxSizing: 'border-box'
              }}
            >
              {cellContent}
            </div>
          );
        })}
      </div>
    );
  }, [columns, processedData]);

  // 如果数据量小于阈值，使用普通表格
  if (processedData.length <= 100) {
    return (
      <Table
        columns={columns}
        dataSource={processedData}
        rowKey={rowKey}
        loading={loading}
        pagination={pagination}
        scroll={{ y: height }}
        locale={{ emptyText: <Empty description={emptyText} /> }}
        {...tableProps}
      />
    );
  }

  // 大数据量使用虚拟化
  return (
    <div className="virtualized-table-container">
      {/* 表头 */}
      <div className="virtual-table-header" style={{ 
        borderBottom: '2px solid #f0f0f0',
        backgroundColor: '#fafafa',
        fontWeight: 600
      }}>
        {columns.map((column, index) => (
          <div
            key={index}
            className="virtual-table-header-cell"
            style={{
              width: column.width || 100,
              textAlign: column.align || 'left',
              padding: '12px',
              display: 'inline-block',
              borderRight: '1px solid #f0f0f0',
              boxSizing: 'border-box'
            }}
          >
            {column.title}
          </div>
        ))}
      </div>

      {/* 虚拟化表格内容 */}
      {processedData.length > 0 ? (
        <List
          height={height}
          itemCount={processedData.length}
          itemSize={rowHeight}
          overscanCount={5} // 预渲染5行以提升滚动体验
        >
          {RowRenderer}
        </List>
      ) : (
        <div style={{ 
          height: height, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center' 
        }}>
          <Empty description={emptyText} />
        </div>
      )}

      {/* 加载状态 */}
      {loading && (
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 10
        }}>
          <div>加载中...</div>
        </div>
      )}
    </div>
  );
};

export default VirtualizedTable;
