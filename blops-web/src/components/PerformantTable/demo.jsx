import React from 'react';
import PerformantTable from './index';

// 生成测试数据
const generateTestData = (count) => {
  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    name: `Topic_${index + 1}`,
    partitions: Math.floor(Math.random() * 10) + 1,
    replicas: Math.floor(Math.random() * 3) + 1,
    description: `Description for topic ${index + 1}`,
  }));
};

// 测试用的表格列定义
const testColumns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
  },
  {
    title: 'Topic名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
  },
  {
    title: '分区数',
    dataIndex: 'partitions',
    key: 'partitions',
    width: 100,
    align: 'center',
    render: (partitions) => (
      <span style={{ 
        color: '#52c41a', 
        fontWeight: 600,
        padding: '2px 8px',
        backgroundColor: '#f6ffed',
        borderRadius: '4px',
        fontSize: '12px'
      }}>
        {partitions}
      </span>
    ),
  },
  {
    title: '副本数',
    dataIndex: 'replicas',
    key: 'replicas',
    width: 100,
    align: 'center',
    render: (replicas) => (
      <span style={{ 
        color: '#1890ff', 
        fontWeight: 600,
        padding: '2px 8px',
        backgroundColor: '#f0f8ff',
        borderRadius: '4px',
        fontSize: '12px'
      }}>
        {replicas}
      </span>
    ),
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true,
  },
];

// 性能测试组件
const PerformantTableDemo = () => {
  // 生成大量测试数据 (模拟真实场景)
  const testData = generateTestData(1000);

  return (
    <div style={{ padding: '20px' }}>
      <h2>PerformantTable 性能测试</h2>
      <p>测试数据: {testData.length} 条记录</p>
      
      <PerformantTable
        columns={testColumns}
        dataSource={testData}
        rowKey="id"
        searchable={true}
        searchPlaceholder="搜索Topic名称..."
        defaultPageSize={50}
      />
    </div>
  );
};

export default PerformantTableDemo;
