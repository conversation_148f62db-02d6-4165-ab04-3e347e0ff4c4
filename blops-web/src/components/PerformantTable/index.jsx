import React, { useMemo, useCallback, useState } from 'react';
import { Table, Input, Space, Button } from 'antd';
import { SearchOutlined, ClearOutlined } from '@ant-design/icons';
import './index.less';

const { Search } = Input;

// 高性能表格组件 - 针对大数据集优化
const PerformantTable = ({
  columns,
  dataSource = [],
  rowKey = 'id',
  searchable = true,
  searchPlaceholder = '搜索...',
  defaultPageSize = 50,
  loading = false,
  ...tableProps
}) => {
  const [searchText, setSearchText] = useState('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: defaultPageSize,
    showSizeChanger: true,
    showQuickJumper: true,
    pageSizeOptions: ['20', '50', '100', '200'],
    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
  });

  // 使用useMemo缓存搜索结果，避免重复计算
  const filteredData = useMemo(() => {
    if (!searchText.trim()) return dataSource;
    
    const searchLower = searchText.toLowerCase();
    return dataSource.filter(item => {
      // 搜索所有可搜索的字段
      return columns.some(column => {
        if (!column.dataIndex) return false;
        const value = item[column.dataIndex];
        if (value == null) return false;
        return String(value).toLowerCase().includes(searchLower);
      });
    });
  }, [dataSource, searchText, columns]);

  // 优化的分页处理
  const handleTableChange = useCallback((paginationConfig) => {
    setPagination(prev => ({
      ...prev,
      ...paginationConfig,
    }));
  }, []);

  // 搜索处理
  const handleSearch = useCallback((value) => {
    setSearchText(value);
    // 搜索时重置到第一页
    setPagination(prev => ({
      ...prev,
      current: 1,
    }));
  }, []);

  // 清除搜索
  const handleClearSearch = useCallback(() => {
    setSearchText('');
    setPagination(prev => ({
      ...prev,
      current: 1,
    }));
  }, []);

  // 优化的表格配置
  const optimizedTableProps = useMemo(() => ({
    size: 'small',
    scroll: { y: 350, x: 'max-content' },
    pagination: {
      ...pagination,
      total: filteredData.length,
    },
    loading,
    locale: {
      emptyText: searchText ? `未找到包含"${searchText}"的数据` : '暂无数据'
    },
    ...tableProps,
  }), [pagination, filteredData.length, loading, searchText, tableProps]);

  return (
    <div className="performant-table-container">
      {/* 搜索栏 */}
      {searchable && (
        <div className="table-search-bar">
          <Space>
            <Search
              placeholder={searchPlaceholder}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onSearch={handleSearch}
              style={{ width: 250 }}
              allowClear
              enterButton={<SearchOutlined />}
            />
            {searchText && (
              <Button
                size="small"
                icon={<ClearOutlined />}
                onClick={handleClearSearch}
              >
                清除
              </Button>
            )}
            <span className="search-result-info">
              {searchText && (
                <span style={{ color: '#8c8c8c', fontSize: '12px' }}>
                  找到 {filteredData.length} 条结果
                </span>
              )}
            </span>
          </Space>
        </div>
      )}

      {/* 优化的表格 */}
      <Table
        columns={columns}
        dataSource={filteredData}
        rowKey={rowKey}
        onChange={handleTableChange}
        {...optimizedTableProps}
      />
    </div>
  );
};

export default PerformantTable;
