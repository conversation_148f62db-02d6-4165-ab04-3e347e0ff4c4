.performant-table-container {
  .table-search-bar {
    margin-bottom: 16px;
    padding: 12px 16px;
    background: #fafafa;
    border-radius: 6px;
    border: 1px solid #f0f0f0;

    .search-result-info {
      margin-left: 12px;
    }
  }

  // 表格性能优化样式
  .ant-table {
    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 600;
      font-size: 13px;
      padding: 8px 12px;
    }

    .ant-table-tbody > tr > td {
      padding: 6px 12px;
      font-size: 12px;
      line-height: 1.4;
    }

    // 优化表格行的hover效果
    .ant-table-tbody > tr:hover > td {
      background: #f5f5f5 !important;
    }

    // 优化分页器样式
    .ant-pagination {
      margin-top: 16px;
      text-align: right;

      .ant-pagination-total-text {
        font-size: 12px;
        color: #8c8c8c;
      }
    }
  }

  // 加载状态优化
  .ant-spin-container {
    min-height: 200px;
  }

  // 空状态优化
  .ant-empty {
    margin: 40px 0;
    
    .ant-empty-description {
      color: #8c8c8c;
      font-size: 13px;
    }
  }
}
