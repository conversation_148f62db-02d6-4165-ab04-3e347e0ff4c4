export default [
  {
    name: 'login',
    path: '/user/login',
    layout: false,
    hideInMenu: true,
    component: './user/Login',
  },
  {
    path: '/',
    hideInMenu: true,
    name: '个人中心',
    icon: 'smile',
    component: './user/Profile',
  },
  {
    path: '/user/profile',
    hideInMenu: true,
    name: '个人中心',
    icon: 'smile',
    component: './user/Profile',
  },
  {
    name: '资源管理',
    path: '/resource',
    routes: [
      {
        name: '主机列表',
        path: '/resource/host/_list',
        component: './resource/HostList',
      },
      {
        name: '中间件',
        path: '/resource/middleware',
        routes: [
          {
            name: '环境及链接',
            path: '/resource/middleware/env-links',
            component: './middleware/EnvLinks',
          },
          {
            name: 'kibana',
            path: '/resource/middleware/kibana',
            component: './middleware/Kibana',
          },
          {
            name: 'prometheus',
            path: '/resource/middleware/prometheus',
            component: './middleware/Prometheus',
          },
          {
            name: 'kafka manager',
            path: '/resource/middleware/kafka-manager',
            component: './middleware/KafkaManager',
          },
          {
            name: 'rocket manager',
            path: '/resource/middleware/rocket-manager',
            component: './middleware/RocketManager',
          },
          {
            name: 'topic 管理',
            path: '/resource/middleware/topic-manager',
            component: './middleware/TopicManager',
          }
        ],
      },
    ],
  },
  {
    name: '监控告警',
    path: '/alert',
    routes: [
      {
        name: '实时看板',
        path: '/alert/dashboard',
        component: './alert/AlertDashboard',
      },
      {
        name: '告警列表',
        path: '/alert/all',
        component: './alert/AlertList',
      },
      {
        name: '告警规则配置',
        path: '/alert/conf',
        component: './alert/Conf',
      },
      {
        name: '告警模板配置',
        path: '/alert/expr',
        component: './alert/Expr',
      },
      // {
      //   name: '规则测试页面',
      //   path: '/alert/testr',
      //   component: './alert/TestRule',
      // },
      // {
      //   name: '模板测试页面',
      //   path: '/alert/test',
      //   component: './alert/TestA',
      // },
    ],
  },
  {
    name: '服务安装',
    path: '/ansible',
    routes: [
      {
        name: '服务列表',
        path: '/ansible/server/_list',
        component: './ansible/ServerList',
      },
    ],
  },
  {
    name: 'k8s管理',
    path: '/cluster',
    // access: 'canAccessKubernetes',
    routes: [
      {
        name: '集群列表',
        path: '/cluster/list',
        component: './cluster/index',
        access: 'canAccessKubernetesList',
      },
      {
        name: 'AI诊断',
        path: '/cluster/ai-diagnosis',
        component: './cluster/ai-diagnosis',
        // access: 'canAccessKubernetesList',
      },
      {
        name: 'Pod管理',
        path: '/cluster/pod-management',
        component: './cluster/pod-management',
        // 如有权限需求，可添加: access: 'canAccessKubernetesList',
      },
      {
        name: 'Ingress域名',
        path: '/cluster/ingress-domains',
        component: './cluster/IngressDomainsPage',
        // 如有权限需求，可添加: access: 'canAccessKubernetesList',
      },
    ],
  },
  {
    name: '文件管理',
    path: '/file-management',
    routes: [
      {
        name: '内存dump文件',
        path: '/file-management/dump',
        component: './file-management',
      },
    ],
  },
  {
    name: '定时任务',
    path: '/cronjob',
    component: './cronjob',
  },
  {
    path: '/',
    redirect: '/user/profile',
  },
  {
    name: '应用市场',
    path: '/appmarket',
    component: './appmarket',
  },
  {
    component: './404',
  },
];
