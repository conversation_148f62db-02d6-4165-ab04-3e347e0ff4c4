-- Topic缓存相关表结构
-- 创建时间: 2024-01-01
-- 描述: 用于缓存Kafka和RocketMQ的Topic和Group信息，提高查询性能

-- Topic缓存表
CREATE TABLE IF NOT EXISTS `t_topic_cache` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `config_id` bigint(20) NOT NULL COMMENT 'Topic配置ID',
  `name` varchar(255) NOT NULL COMMENT 'Topic名称',
  `partitions` int(11) NOT NULL DEFAULT '0' COMMENT '分区数',
  `replicas` int(11) NOT NULL DEFAULT '0' COMMENT '副本数',
  `description` text COMMENT '描述信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_config_id` (`config_id`),
  <PERSON>EY `idx_name` (`name`),
  CONSTRAINT `fk_topic_cache_config` FOREIGN KEY (`config_id`) REFERENCES `t_topic_config` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Topic缓存表';

-- Group缓存表
CREATE TABLE IF NOT EXISTS `t_group_cache` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `config_id` bigint(20) NOT NULL COMMENT 'Topic配置ID',
  `name` varchar(255) NOT NULL COMMENT 'Group名称',
  `state` varchar(50) DEFAULT '' COMMENT '状态',
  `members` int(11) NOT NULL DEFAULT '0' COMMENT '成员数',
  `description` text COMMENT '描述信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_name` (`name`),
  CONSTRAINT `fk_group_cache_config` FOREIGN KEY (`config_id`) REFERENCES `t_topic_config` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Group缓存表';

-- 缓存元数据表
CREATE TABLE IF NOT EXISTS `t_topic_group_cache_meta` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `config_id` bigint(20) NOT NULL COMMENT 'Topic配置ID',
  `last_sync_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后同步时间',
  `topic_count` int(11) NOT NULL DEFAULT '0' COMMENT 'Topic数量',
  `group_count` int(11) NOT NULL DEFAULT '0' COMMENT 'Group数量',
  `sync_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '同步状态：success, failed, syncing, pending',
  `error_message` text COMMENT '错误信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_id` (`config_id`),
  CONSTRAINT `fk_cache_meta_config` FOREIGN KEY (`config_id`) REFERENCES `t_topic_config` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Topic和Group缓存元数据表';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS `idx_topic_cache_config_name` ON `t_topic_cache` (`config_id`, `name`);
CREATE INDEX IF NOT EXISTS `idx_group_cache_config_name` ON `t_group_cache` (`config_id`, `name`);
CREATE INDEX IF NOT EXISTS `idx_cache_meta_sync_status` ON `t_topic_group_cache_meta` (`sync_status`);
CREATE INDEX IF NOT EXISTS `idx_cache_meta_last_sync` ON `t_topic_group_cache_meta` (`last_sync_at`);
