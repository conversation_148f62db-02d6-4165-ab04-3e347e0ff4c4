-- 创建同步任务表
CREATE TABLE IF NOT EXISTS `t_sync_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `name` varchar(255) NOT NULL COMMENT '任务名称',
  `type` varchar(50) NOT NULL COMMENT '同步类型：kafka 或 rocketmq',
  `source_config_id` bigint(20) NOT NULL COMMENT '源配置ID',
  `target_config_id` bigint(20) NOT NULL COMMENT '目标配置ID',
  `sync_topics` tinyint(1) DEFAULT 0 COMMENT '是否同步Topic',
  `sync_groups` tinyint(1) DEFAULT 0 COMMENT '是否同步Group',
  `skip_existing` tinyint(1) DEFAULT 1 COMMENT '是否跳过已存在的项目',
  `status` varchar(50) DEFAULT 'pending' COMMENT '任务状态：pending, running, completed, failed, cancelled',
  `progress` int(11) DEFAULT 0 COMMENT '进度百分比',
  `total_items` int(11) DEFAULT 0 COMMENT '总项目数',
  `completed_items` int(11) DEFAULT 0 COMMENT '已完成项目数',
  `skipped_items` int(11) DEFAULT 0 COMMENT '已跳过项目数',
  `failed_items` int(11) DEFAULT 0 COMMENT '失败项目数',
  `error_message` text COMMENT '错误信息',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `started_at` datetime DEFAULT NULL COMMENT '开始时间',
  `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_source_config` (`source_config_id`),
  KEY `idx_target_config` (`target_config_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_sync_task_source_config` FOREIGN KEY (`source_config_id`) REFERENCES `t_topic_config` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_sync_task_target_config` FOREIGN KEY (`target_config_id`) REFERENCES `t_topic_config` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='同步任务表';

-- 创建同步项目表
CREATE TABLE IF NOT EXISTS `t_sync_item` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `task_id` bigint(20) NOT NULL COMMENT '所属任务ID',
  `item_type` varchar(50) NOT NULL COMMENT '项目类型：topic 或 group',
  `item_name` varchar(255) NOT NULL COMMENT '项目名称',
  `status` varchar(50) DEFAULT 'pending' COMMENT '状态：pending, completed, skipped, failed',
  `error_msg` text COMMENT '错误信息',
  `source_data` text COMMENT '源数据（JSON格式）',
  `target_data` text COMMENT '目标数据（JSON格式）',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_item_type` (`item_type`),
  KEY `idx_status` (`status`),
  KEY `idx_item_name` (`item_name`),
  CONSTRAINT `fk_sync_item_task` FOREIGN KEY (`task_id`) REFERENCES `t_sync_task` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='同步项目表';

-- 创建同步日志表
CREATE TABLE IF NOT EXISTS `t_sync_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `task_id` bigint(20) NOT NULL COMMENT '所属任务ID',
  `level` varchar(20) NOT NULL COMMENT '日志级别：info, warn, error',
  `message` varchar(500) NOT NULL COMMENT '日志消息',
  `details` text COMMENT '详细信息',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_level` (`level`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_sync_log_task` FOREIGN KEY (`task_id`) REFERENCES `t_sync_task` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='同步日志表';

-- 创建索引以优化查询性能
CREATE INDEX `idx_sync_task_composite` ON `t_sync_task` (`type`, `status`, `created_at`);
CREATE INDEX `idx_sync_item_composite` ON `t_sync_item` (`task_id`, `item_type`, `status`);
CREATE INDEX `idx_sync_log_composite` ON `t_sync_log` (`task_id`, `level`, `created_at`);
