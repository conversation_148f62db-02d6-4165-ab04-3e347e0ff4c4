-- 优化Topic缓存相关表的索引
-- 解决慢SQL查询问题

-- 1. 优化 t_topic_group_cache_meta 表
-- 添加复合索引以优化常用查询
ALTER TABLE `t_topic_group_cache_meta` 
ADD INDEX `idx_config_sync_status` (`config_id`, `sync_status`),
ADD INDEX `idx_last_sync_at` (`last_sync_at`),
ADD INDEX `idx_updated_at` (`updated_at`);

-- 2. 优化 t_topic_cache 表
-- 添加复合索引以优化批量查询
ALTER TABLE `t_topic_cache` 
ADD INDEX `idx_config_name` (`config_id`, `name`),
ADD INDEX `idx_created_at` (`created_at`);

-- 3. 优化 t_group_cache 表
-- 添加复合索引以优化批量查询
ALTER TABLE `t_group_cache` 
ADD INDEX `idx_config_name` (`config_id`, `name`),
ADD INDEX `idx_config_state` (`config_id`, `state`),
ADD INDEX `idx_created_at` (`created_at`);

-- 4. 优化 t_topic_config 表
-- 添加类型和地址的索引以优化查询
ALTER TABLE `t_topic_config` 
ADD INDEX `idx_type` (`type`),
ADD INDEX `idx_address` (`address`(100)), -- 限制长度避免索引过大
ADD INDEX `idx_created_at` (`created_at`);

-- 5. 分析表以更新统计信息
ANALYZE TABLE `t_topic_group_cache_meta`;
ANALYZE TABLE `t_topic_cache`;
ANALYZE TABLE `t_group_cache`;
ANALYZE TABLE `t_topic_config`;

-- 6. 查看索引使用情况的查询（用于监控）
-- SELECT 
--   TABLE_NAME,
--   INDEX_NAME,
--   CARDINALITY,
--   SUB_PART,
--   PACKED,
--   NULLABLE,
--   INDEX_TYPE
-- FROM information_schema.STATISTICS 
-- WHERE TABLE_SCHEMA = 'blops_admin' 
--   AND TABLE_NAME IN ('t_topic_group_cache_meta', 't_topic_cache', 't_group_cache', 't_topic_config')
-- ORDER BY TABLE_NAME, INDEX_NAME;
