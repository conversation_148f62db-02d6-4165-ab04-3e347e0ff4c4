-- 创建Topic配置表
CREATE TABLE IF NOT EXISTS `t_topic_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `env` bigint(20) NOT NULL COMMENT '所属环境ID',
  `name` varchar(255) NOT NULL COMMENT '配置名称',
  `type` varchar(50) NOT NULL COMMENT '类型：kafka 或 rocketmq',
  `address` varchar(500) NOT NULL COMMENT '连接地址',
  `username` varchar(255) DEFAULT NULL COMMENT '用户名（可选）',
  `password` varchar(255) DEFAULT NULL COMMENT '密码（可选）',
  `description` text COMMENT '描述信息',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_env` (`env`),
  KEY `idx_type` (`type`),
  KEY `idx_name` (`name`),
  CONSTRAINT `fk_topic_config_env` FOREIGN KEY (`env`) REFERENCES `t_middleware_env` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Topic配置表';

-- 插入一些示例数据（可选）
INSERT INTO `t_topic_config` (`env`, `name`, `type`, `address`, `username`, `description`) VALUES
(1, 'Kafka测试环境', 'kafka', 'localhost:9092', '', 'Kafka测试环境配置'),
(1, 'RocketMQ测试环境', 'rocketmq', 'localhost:9876', '', 'RocketMQ测试环境配置');
