package router

import (
	"blops/app/controllers"

	"github.com/gin-gonic/gin"
)

// TopicRouter Topic管理路由定义
func TopicRouter(r *gin.RouterGroup) {
	g := r.Group("/v1/topic")
	{
		// Topic配置相关路由
		g.GET("/config", controllers.TopicCtrl.ListTopicConfigs)
		g.GET("/config/:id", controllers.TopicCtrl.GetTopicConfig)
		g.POST("/config", controllers.TopicCtrl.CreateTopicConfig)
		g.PUT("/config", controllers.TopicCtrl.UpdateTopicConfig)
		g.DELETE("/config/:id", controllers.TopicCtrl.DeleteTopicConfig)
		g.POST("/config/by-type", controllers.TopicCtrl.ListTopicConfigsByType)
		g.POST("/config/by-env", controllers.TopicCtrl.ListTopicConfigsByEnv)
		g.POST("/config/by-type-env", controllers.TopicCtrl.ListTopicConfigsByTypeAndEnv)

		// Topic和Group查询相关路由
		g.GET("/config/:id/topics-groups", controllers.TopicCtrl.GetTopicsAndGroups)
		g.POST("/config/:id/topics-groups/refresh", controllers.TopicCtrl.RefreshTopicsAndGroups)

		// 连接测试路由
		g.POST("/config/test-connection", controllers.TopicCtrl.TestTopicConfigConnection)
	}
}
