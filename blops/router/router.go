package router

import (
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

func Generate(r *gin.Engine) {
	// 添加 Swagger 路由
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// API 路由组
	g := r.Group("/api")
	{
		ResourceRouter(g)
		UserRouter(g)
		PodRouter(g)
	}
	{
		AlertRouter(g)
	}
	{
		ClusterRouter(g)
	}
	{
		AIDiagnosisRouter(g)
	}
	{
		CronJobRouter(g)
	}
	{
		AppMarketRouter(g)
	}
	{
		MiddlewareRouter(g)
	}
	{
		TopicRouter(g)
	}
	{
		SyncRouter(g)
	}
}
