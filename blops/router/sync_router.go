package router

import (
	"blops/app/controllers"

	"github.com/gin-gonic/gin"
)

// SyncRouter 同步管理路由定义
func SyncRouter(r *gin.RouterGroup) {
	g := r.Group("/v1/sync")
	{
		// 同步任务管理路由
		g.GET("/task", controllers.SyncCtrl.ListSyncTasks)
		g.GET("/task/:id", controllers.SyncCtrl.GetSyncTask)
		g.POST("/task", controllers.SyncCtrl.CreateSyncTask)
		g.DELETE("/task/:id", controllers.SyncCtrl.DeleteSyncTask)
		g.POST("/task/:id/cancel", controllers.SyncCtrl.CancelSyncTask)

		// 同步预览和执行路由
		g.POST("/preview", controllers.SyncCtrl.PreviewSync)
		g.POST("/task/:id/execute", controllers.SyncCtrl.ExecuteSync)

		// 同步项目和日志路由
		g.GET("/task/:id/items", controllers.SyncCtrl.GetSyncItems)
		g.GET("/task/:id/logs", controllers.SyncCtrl.GetSyncLogs)
		g.GET("/task/:id/stats", controllers.SyncCtrl.GetSyncStats)
	}
}
