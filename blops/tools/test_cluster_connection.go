package main

import (
	"flag"
	"fmt"
	"strings"
	"time"

	"github.com/IBM/sarama"
)

func main() {
	var (
		singleBroker   = flag.String("single", "10.83.31.57:9092", "单个broker地址")
		clusterBrokers = flag.String("cluster", "10.83.31.57:9092,10.83.31.58:9092,10.83.31.59:9092", "集群broker地址")
		username       = flag.String("username", "", "用户名")
		password       = flag.String("password", "", "密码")
	)
	flag.Parse()

	fmt.Printf("=== Kafka 集群连接对比测试 ===\n\n")

	// 测试单个broker连接
	fmt.Printf("1. 测试单个broker连接: %s\n", *singleBroker)
	testConnection("单个broker", *singleBroker, *username, *password)

	fmt.Printf("\n" + strings.Repeat("=", 50) + "\n\n")

	// 测试集群连接
	fmt.Printf("2. 测试集群连接: %s\n", *clusterBrokers)
	testConnection("集群", *clusterBrokers, *username, *password)
}

func testConnection(name, brokers, username, password string) {
	config := sarama.NewConfig()
	config.Net.DialTimeout = 30 * time.Second
	config.Net.ReadTimeout = 30 * time.Second
	config.Net.WriteTimeout = 30 * time.Second

	// 设置更宽松的连接参数
	config.Net.KeepAlive = 30 * time.Second
	config.Metadata.Retry.Max = 3
	config.Metadata.Retry.Backoff = 250 * time.Millisecond
	config.Metadata.RefreshFrequency = 10 * time.Minute
	config.Metadata.Full = true

	// 设置版本兼容性和客户端ID
	config.Version = sarama.V2_6_0_0
	config.ClientID = "blops-test-client"

	if username != "" && password != "" {
		config.Net.SASL.Enable = true
		config.Net.SASL.Mechanism = sarama.SASLTypePlaintext
		config.Net.SASL.User = username
		config.Net.SASL.Password = password
	}

	// 添加重试机制
	var lastErr error
	for i := 0; i < 3; i++ {
		fmt.Printf("  尝试 %d/3...\n", i+1)

		admin, err := sarama.NewClusterAdmin(strings.Split(brokers, ","), config)
		if err != nil {
			lastErr = fmt.Errorf("创建管理员客户端失败: %w", err)
			fmt.Printf("  ❌ %v\n", lastErr)
			if i < 2 {
				time.Sleep(time.Duration(i+1) * time.Second)
				continue
			}
			break
		}

		// 尝试获取Topic列表
		topics, err := admin.ListTopics()
		if err != nil {
			admin.Close()
			lastErr = fmt.Errorf("获取Topic列表失败: %w", err)
			fmt.Printf("  ❌ %v\n", lastErr)
			if i < 2 {
				time.Sleep(time.Duration(i+1) * time.Second)
				continue
			}
			break
		}

		// 获取消费者组
		groups, err := admin.ListConsumerGroups()
		admin.Close()

		if err != nil {
			fmt.Printf("  ⚠ 获取消费者组失败: %v\n", err)
		}

		// 统计非内部topic
		topicCount := 0
		for topicName := range topics {
			if !strings.HasPrefix(topicName, "__") {
				topicCount++
			}
		}

		fmt.Printf("  ✅ %s连接成功!\n", name)
		fmt.Printf("     - Topics: %d 个\n", topicCount)
		fmt.Printf("     - 消费者组: %d 个\n", len(groups))
		fmt.Printf("     - 重试次数: %d\n", i+1)
		return
	}

	fmt.Printf("  ❌ %s连接失败: %v\n", name, lastErr)
}
