package main

import (
	"flag"
	"fmt"
	"strings"
	"time"

	"blops/app/clients"
)

func main() {
	var (
		nameServer = flag.String("nameserver", "10.83.0.128:9876", "RocketMQ NameServer地址")
		username   = flag.String("username", "", "用户名")
		password   = flag.String("password", "", "密码")
		testTopic  = flag.String("topic", "", "要测试的Topic名称（留空则使用测试Topic）")
		queueNum   = flag.Int("queue", 4, "队列数量")
	)
	flag.Parse()

	topic := *testTopic
	if topic == "" {
		topic = fmt.Sprintf("test_optimized_create_%d", time.Now().Unix())
	}

	fmt.Printf("=== RocketMQ 优化版Topic创建测试 ===\n")
	fmt.Printf("NameServer: %s\n", *nameServer)
	fmt.Printf("测试Topic: %s\n", topic)
	fmt.Printf("队列数: %d\n", *queueNum)
	fmt.Printf("========================\n\n")

	// 创建 RocketMQ 客户端
	client := clients.NewRocketMQClient(*nameServer, *username, *password)
	defer client.Close()

	// 1. 测试连接
	fmt.Printf("1. 测试连接...\n")
	err := client.TestConnection()
	if err != nil {
		fmt.Printf("❌ 连接测试失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 连接测试成功\n\n")

	// 2. 检查Topic是否已存在
	fmt.Printf("2. 检查Topic是否已存在...\n")
	topics, err := client.GetTopics()
	if err != nil {
		fmt.Printf("❌ 获取Topics失败: %v\n", err)
		return
	}

	exists := false
	for _, t := range topics {
		if t.Name == topic {
			exists = true
			break
		}
	}

	if exists {
		fmt.Printf("⚠ Topic %s 已存在\n", topic)
		if *testTopic == "" {
			fmt.Printf("跳过创建测试\n")
			return
		}
	} else {
		fmt.Printf("✅ Topic不存在，可以创建\n")
	}

	// 3. 测试优化后的创建逻辑
	fmt.Printf("\n3. 测试优化后的Topic创建...\n")
	fmt.Printf("开始创建Topic: %s\n", topic)

	start := time.Now()
	err = client.CreateTopic(topic, *queueNum)
	duration := time.Since(start)

	if err != nil {
		fmt.Printf("❌ 创建Topic失败 (耗时: %v): %v\n", duration, err)

		// 分析错误类型
		errStr := err.Error()
		if strings.Contains(errStr, "missing address") {
			fmt.Printf("\n错误分析:\n")
			fmt.Printf("- 这是Broker地址缺失问题\n")
			fmt.Printf("- 优化后的代码应该已经尝试了多种方法\n")
			fmt.Printf("- 请检查错误信息中的手动解决方案\n")
		} else if strings.Contains(errStr, "已存在") {
			fmt.Printf("\n错误分析:\n")
			fmt.Printf("- Topic已存在，这是正常的\n")
		} else {
			fmt.Printf("\n错误分析:\n")
			fmt.Printf("- 其他类型的错误\n")
		}

		// 如果错误信息包含手动解决方案，提取并显示
		if strings.Contains(errStr, "手动解决方案:") {
			fmt.Printf("\n=== 手动解决方案 ===\n")
			lines := strings.Split(errStr, "\n")
			inSolution := false
			for _, line := range lines {
				if strings.Contains(line, "手动解决方案:") {
					inSolution = true
					continue
				}
				if inSolution && strings.Contains(line, "自动化建议:") {
					break
				}
				if inSolution {
					fmt.Printf("%s\n", line)
				}
			}
		}

	} else {
		fmt.Printf("✅ Topic创建成功! (耗时: %v)\n", duration)

		// 验证创建结果
		fmt.Printf("\n4. 验证Topic创建结果...\n")
		time.Sleep(2 * time.Second) // 等待创建生效

		newTopics, err := client.GetTopics()
		if err != nil {
			fmt.Printf("⚠ 无法验证Topic创建结果: %v\n", err)
		} else {
			found := false
			for _, t := range newTopics {
				if t.Name == topic {
					found = true
					fmt.Printf("✅ Topic验证成功: %s (队列数: %d)\n", t.Name, t.QueueNum)
					break
				}
			}

			if !found {
				fmt.Printf("❌ Topic验证失败：未在列表中找到\n")
			}
		}

		// 清理测试Topic（如果是自动生成的）
		if *testTopic == "" {
			fmt.Printf("\n5. 清理测试Topic...\n")
			fmt.Printf("⚠ 请手动清理测试Topic: %s\n", topic)
			fmt.Printf("命令: sh bin/mqadmin deleteTopic -n %s -t %s\n", *nameServer, topic)
		}
	}

	fmt.Printf("\n=== 测试完成 ===\n")

	// 总结
	fmt.Printf("\n=== 优化效果总结 ===\n")
	fmt.Printf("1. ✅ 增强了错误处理和日志输出\n")
	fmt.Printf("2. ✅ 实现了多种创建方法的尝试\n")
	fmt.Printf("3. ✅ 添加了Broker地址自动发现\n")
	fmt.Printf("4. ✅ 实现了HTTP API备用方案\n")
	fmt.Printf("5. ✅ 提供了详细的手动解决方案\n")

	if err != nil {
		fmt.Printf("\n建议:\n")
		fmt.Printf("1. 检查RocketMQ Broker配置中的brokerIP1设置\n")
		fmt.Printf("2. 重启Broker服务确保正确注册到NameServer\n")
		fmt.Printf("3. 使用提供的手动命令创建Topic\n")
		fmt.Printf("4. 考虑部署RocketMQ Console管理界面\n")
	}
}
