package main

import (
	"context"
	"flag"
	"fmt"
	"net"
	"strings"
	"time"

	"github.com/apache/rocketmq-client-go/v2/admin"
	"github.com/apache/rocketmq-client-go/v2/primitive"
)

func main() {
	var (
		nameServer = flag.String("nameserver", "10.83.0.128:9876", "RocketMQ NameServer地址")
		username   = flag.String("username", "", "用户名")
		password   = flag.String("password", "", "密码")
		testTopic  = flag.String("topic", "", "要测试的Topic名称（留空则使用测试Topic）")
		queueNum   = flag.Int("queue", 4, "队列数量")
	)
	flag.Parse()

	topic := *testTopic
	if topic == "" {
		topic = fmt.Sprintf("test_optimized_%d", time.Now().Unix())
	}

	fmt.Printf("=== RocketMQ 优化版Topic创建测试 ===\n")
	fmt.Printf("NameServer: %s\n", *nameServer)
	fmt.Printf("测试Topic: %s\n", topic)
	fmt.Printf("队列数: %d\n", *queueNum)
	fmt.Printf("========================\n\n")

	// 创建Admin客户端
	var mqAdmin admin.Admin
	var err error

	if *username != "" && *password != "" {
		mqAdmin, err = admin.NewAdmin(
			admin.WithResolver(primitive.NewPassthroughResolver(strings.Split(*nameServer, ","))),
			admin.WithCredentials(primitive.Credentials{
				AccessKey: *username,
				SecretKey: *password,
			}),
		)
	} else {
		mqAdmin, err = admin.NewAdmin(
			admin.WithResolver(primitive.NewPassthroughResolver(strings.Split(*nameServer, ","))),
		)
	}

	if err != nil {
		fmt.Printf("❌ 创建Admin客户端失败: %v\n", err)
		return
	}
	defer mqAdmin.Close()

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 1. 测试获取Topic列表
	fmt.Printf("1. 测试获取Topic列表...\n")
	topicList, err := mqAdmin.FetchAllTopicList(ctx)
	if err != nil {
		fmt.Printf("❌ 获取Topic列表失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 成功获取 %d 个Topics\n\n", len(topicList.TopicList))

	// 2. 检查Topic是否已存在
	fmt.Printf("2. 检查Topic是否已存在...\n")
	exists := false
	for _, t := range topicList.TopicList {
		if t == topic {
			exists = true
			break
		}
	}

	if exists {
		fmt.Printf("⚠ Topic %s 已存在，先删除...\n", topic)
		err = mqAdmin.DeleteTopic(ctx, admin.WithTopicDelete(topic))
		if err != nil {
			fmt.Printf("❌ 删除已存在的Topic失败: %v\n", err)
		} else {
			fmt.Printf("✅ 成功删除已存在的Topic\n")
			time.Sleep(2 * time.Second)
		}
	} else {
		fmt.Printf("✅ Topic不存在，可以创建\n")
	}

	// 3. 获取可用的Broker地址
	fmt.Printf("\n3. 获取可用的Broker地址...\n")
	brokerAddrs := getAvailableBrokerAddresses(*nameServer)
	fmt.Printf("找到 %d 个可能的Broker地址: %v\n", len(brokerAddrs), brokerAddrs)

	// 4. 测试优化后的创建逻辑
	fmt.Printf("\n4. 测试优化后的Topic创建...\n")

	// 方法1: 基本创建方法
	fmt.Printf("方法1: 使用基本CreateTopic方法...\n")
	err = mqAdmin.CreateTopic(ctx, admin.WithTopicCreate(topic))
	if err != nil {
		fmt.Printf("❌ 方法1失败: %v\n", err)

		// 方法2: 使用指定Broker地址的方式
		if strings.Contains(err.Error(), "missing address") {
			fmt.Printf("\n方法2: 使用指定Broker地址创建...\n")
			
			success := false
			for _, brokerAddr := range brokerAddrs {
				fmt.Printf("尝试使用Broker地址: %s\n", brokerAddr)
				
				err = mqAdmin.CreateTopic(ctx,
					admin.WithTopicCreate(topic),
					admin.WithBrokerAddrCreate(brokerAddr),
				)
				
				if err != nil {
					fmt.Printf("❌ 使用Broker %s 失败: %v\n", brokerAddr, err)
					continue
				}
				
				fmt.Printf("✅ 使用Broker %s 创建成功!\n", brokerAddr)
				success = true
				break
			}
			
			if !success {
				fmt.Printf("❌ 所有方法都失败了\n")
				
				// 生成手动创建命令
				fmt.Printf("\n=== 手动解决方案 ===\n")
				fmt.Printf("方法1 - 使用mqadmin命令行工具:\n")
				fmt.Printf("cd /path/to/rocketmq\n")
				fmt.Printf("sh bin/mqadmin updateTopic -n %s -t %s -c DefaultCluster -r %d -w %d\n", 
					*nameServer, topic, *queueNum, *queueNum)
				
				fmt.Printf("\n方法2 - 检查Broker配置:\n")
				fmt.Printf("1. 检查broker.conf中的brokerIP1设置\n")
				fmt.Printf("2. 确保brokerIP1=%s\n", strings.Split(*nameServer, ":")[0])
				fmt.Printf("3. 重启Broker服务\n")
				
				return
			}
		}
	} else {
		fmt.Printf("✅ 方法1创建成功!\n")
	}

	// 5. 验证创建结果
	fmt.Printf("\n5. 验证Topic创建结果...\n")
	time.Sleep(2 * time.Second)
	
	newTopicList, err := mqAdmin.FetchAllTopicList(ctx)
	if err != nil {
		fmt.Printf("⚠ 无法验证Topic创建结果: %v\n", err)
	} else {
		found := false
		for _, t := range newTopicList.TopicList {
			if t == topic {
				found = true
				break
			}
		}
		
		if found {
			fmt.Printf("✅ Topic创建验证成功: %s\n", topic)
		} else {
			fmt.Printf("❌ Topic创建验证失败：未在列表中找到\n")
		}
	}

	// 6. 清理测试Topic
	if *testTopic == "" {
		fmt.Printf("\n6. 清理测试Topic...\n")
		err = mqAdmin.DeleteTopic(ctx, admin.WithTopicDelete(topic))
		if err != nil {
			fmt.Printf("⚠ 清理测试Topic失败: %v\n", err)
		} else {
			fmt.Printf("✅ 测试Topic清理成功\n")
		}
	}

	fmt.Printf("\n=== 测试完成 ===\n")
}

// getAvailableBrokerAddresses 获取可用的Broker地址列表
func getAvailableBrokerAddresses(nameServerAddr string) []string {
	var brokerAddrs []string
	
	nameServers := strings.Split(nameServerAddr, ",")
	for _, ns := range nameServers {
		ns = strings.TrimSpace(ns)
		if ns != "" && strings.Contains(ns, ":") {
			host := strings.Split(ns, ":")[0]
			// 尝试常见的broker端口
			commonPorts := []string{"10911", "10909", "10912"}
			for _, port := range commonPorts {
				brokerAddr := host + ":" + port
				// 简单的网络连通性检查
				if testConnectivity(brokerAddr) {
					brokerAddrs = append(brokerAddrs, brokerAddr)
				}
			}
		}
	}
	
	// 如果没有找到可连接的地址，使用默认配置
	if len(brokerAddrs) == 0 {
		nameServers := strings.Split(nameServerAddr, ",")
		if len(nameServers) > 0 {
			host := strings.Split(strings.TrimSpace(nameServers[0]), ":")[0]
			brokerAddrs = append(brokerAddrs, host+":10911") // 默认broker端口
		}
	}
	
	return brokerAddrs
}

// testConnectivity 测试网络连通性
func testConnectivity(addr string) bool {
	conn, err := net.DialTimeout("tcp", addr, 3*time.Second)
	if err != nil {
		return false
	}
	conn.Close()
	return true
}
