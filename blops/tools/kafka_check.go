package main

import (
	"flag"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/IBM/sarama"
)

func main() {
	// 命令行参数
	var (
		address  = flag.String("address", "***********:9092", "Kafka broker地址")
		username = flag.String("username", "", "用户名")
		password = flag.String("password", "", "密码")
		timeout  = flag.Int("timeout", 30, "连接超时时间(秒)")
	)
	flag.Parse()

	fmt.Printf("=== Kafka连接测试工具 ===\n")
	fmt.Printf("地址: %s\n", *address)
	fmt.Printf("用户名: %s\n", *username)
	fmt.Printf("超时时间: %d秒\n", *timeout)
	fmt.Printf("========================\n\n")

	// 创建Kafka配置
	config := sarama.NewConfig()
	config.Net.DialTimeout = time.Duration(*timeout) * time.Second
	config.Net.ReadTimeout = time.Duration(*timeout) * time.Second
	config.Net.WriteTimeout = time.Duration(*timeout) * time.Second

	// 设置更宽松的连接参数
	config.Net.KeepAlive = 30 * time.Second
	config.Metadata.Retry.Max = 3
	config.Metadata.Retry.Backoff = 250 * time.Millisecond
	config.Metadata.RefreshFrequency = 10 * time.Minute
	config.Metadata.Full = true

	// 设置版本兼容性
	config.Version = sarama.V2_6_0_0

	if *username != "" && *password != "" {
		config.Net.SASL.Enable = true
		config.Net.SASL.Mechanism = sarama.SASLTypePlaintext
		config.Net.SASL.User = *username
		config.Net.SASL.Password = *password
	}

	// 测试连接
	fmt.Printf("1. 测试连接...\n")
	start := time.Now()

	admin, err := sarama.NewClusterAdmin(strings.Split(*address, ","), config)
	if err != nil {
		log.Fatalf("连接测试失败: %v", err)
	}
	defer admin.Close()

	// 尝试获取Topic列表验证连接
	_, err = admin.ListTopics()
	if err != nil {
		log.Fatalf("连接测试失败: %v", err)
	}
	fmt.Printf("✓ 连接测试成功 (耗时: %v)\n\n", time.Since(start))

	// 获取Topic列表
	fmt.Printf("2. 获取Topic列表...\n")
	start = time.Now()
	topics, err := admin.ListTopics()
	if err != nil {
		log.Fatalf("获取Topic列表失败: %v", err)
	}

	// 过滤并统计Topic
	var topicList []string
	for topicName := range topics {
		if !strings.HasPrefix(topicName, "__") {
			topicList = append(topicList, topicName)
		}
	}

	fmt.Printf("✓ 成功获取 %d 个Topics (耗时: %v)\n", len(topicList), time.Since(start))

	// 显示前10个Topic
	fmt.Printf("前10个Topics:\n")
	for i, topicName := range topicList {
		if i >= 10 {
			fmt.Printf("... 还有 %d 个Topics\n", len(topicList)-10)
			break
		}
		detail := topics[topicName]
		replicas := 0
		if len(detail.ReplicaAssignment) > 0 {
			for _, replicasList := range detail.ReplicaAssignment {
				if len(replicasList) > replicas {
					replicas = len(replicasList)
				}
			}
		}
		fmt.Printf("  - %s (分区: %d, 副本: %d)\n", topicName, detail.NumPartitions, replicas)
	}
	fmt.Printf("\n")

	// 获取消费者组列表
	fmt.Printf("3. 获取消费者组列表...\n")
	start = time.Now()
	groups, err := admin.ListConsumerGroups()
	if err != nil {
		fmt.Printf("⚠ 获取消费者组失败: %v\n", err)
	} else {
		fmt.Printf("✓ 成功获取 %d 个消费者组 (耗时: %v)\n", len(groups), time.Since(start))

		// 显示前5个消费者组
		fmt.Printf("前5个消费者组:\n")
		count := 0
		for groupName := range groups {
			if count >= 5 {
				fmt.Printf("... 还有 %d 个消费者组\n", len(groups)-5)
				break
			}
			fmt.Printf("  - %s\n", groupName)
			count++
		}
	}

	fmt.Printf("\n=== 测试完成 ===\n")
}
