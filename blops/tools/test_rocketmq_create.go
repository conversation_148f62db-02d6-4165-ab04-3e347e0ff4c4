package main

import (
	"context"
	"flag"
	"fmt"
	"strings"
	"time"

	"github.com/apache/rocketmq-client-go/v2/admin"
	"github.com/apache/rocketmq-client-go/v2/primitive"
)

func main() {
	var (
		nameServer = flag.String("nameserver", "10.83.0.132:9876", "RocketMQ NameServer地址")
		username   = flag.String("username", "", "用户名")
		password   = flag.String("password", "", "密码")
		topicName  = flag.String("topic", "", "要创建的Topic名称（留空则使用测试Topic）")
		timeout    = flag.Int("timeout", 30, "连接超时时间(秒)")
	)
	flag.Parse()

	testTopic := *topicName
	if testTopic == "" {
		testTopic = fmt.Sprintf("test_topic_create_%d", time.Now().Unix())
	}

	fmt.Printf("=== RocketMQ Topic 创建测试 ===\n")
	fmt.Printf("NameServer: %s\n", *nameServer)
	fmt.Printf("测试Topic: %s\n", testTopic)
	fmt.Printf("超时时间: %d秒\n", *timeout)
	fmt.Printf("========================\n\n")

	// 创建Admin客户端
	var mqAdmin admin.Admin
	var err error

	if *username != "" && *password != "" {
		mqAdmin, err = admin.NewAdmin(
			admin.WithResolver(primitive.NewPassthroughResolver(strings.Split(*nameServer, ","))),
			admin.WithCredentials(primitive.Credentials{
				AccessKey: *username,
				SecretKey: *password,
			}),
		)
	} else {
		mqAdmin, err = admin.NewAdmin(
			admin.WithResolver(primitive.NewPassthroughResolver(strings.Split(*nameServer, ","))),
		)
	}

	if err != nil {
		fmt.Printf("❌ 创建Admin客户端失败: %v\n", err)
		return
	}
	defer mqAdmin.Close()

	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(*timeout)*time.Second)
	defer cancel()

	// 1. 检查Topic是否已存在
	fmt.Printf("1. 检查Topic是否已存在...\n")
	topicList, err := mqAdmin.FetchAllTopicList(ctx)
	if err != nil {
		fmt.Printf("❌ 获取Topic列表失败: %v\n", err)
		return
	}

	exists := false
	for _, topic := range topicList.TopicList {
		if topic == testTopic {
			exists = true
			break
		}
	}

	if exists {
		fmt.Printf("⚠ Topic %s 已存在，先删除...\n", testTopic)
		err = mqAdmin.DeleteTopic(ctx, admin.WithTopicDelete(testTopic))
		if err != nil {
			fmt.Printf("❌ 删除已存在的Topic失败: %v\n", err)
		} else {
			fmt.Printf("✅ 成功删除已存在的Topic\n")
			time.Sleep(2 * time.Second) // 等待删除生效
		}
	} else {
		fmt.Printf("✅ Topic不存在，可以创建\n")
	}

	// 2. 尝试创建Topic
	fmt.Printf("\n2. 创建Topic...\n")
	
	// 方法1：使用基本的CreateTopic
	fmt.Printf("方法1：使用基本CreateTopic...\n")
	err = mqAdmin.CreateTopic(ctx, admin.WithTopicCreate(testTopic))
	if err != nil {
		fmt.Printf("❌ 方法1失败: %v\n", err)
		
		// 分析错误
		errStr := err.Error()
		if strings.Contains(errStr, "missing address") {
			fmt.Printf("  - 错误分析：Broker地址缺失\n")
			fmt.Printf("  - 可能原因：NameServer没有返回有效的Broker信息\n")
		}
		
		// 方法2：尝试不同的创建方式
		fmt.Printf("\n方法2：尝试其他创建方式...\n")
		
		// 这里可以尝试其他方法，但由于API限制，我们先检查是否有其他选项
		fmt.Printf("由于RocketMQ Go客户端API限制，无法尝试其他方法\n")
		
	} else {
		fmt.Printf("✅ Topic创建成功!\n")
		
		// 3. 验证Topic是否真的创建成功
		fmt.Printf("\n3. 验证Topic创建...\n")
		time.Sleep(2 * time.Second) // 等待创建生效
		
		topicList, err = mqAdmin.FetchAllTopicList(ctx)
		if err != nil {
			fmt.Printf("❌ 重新获取Topic列表失败: %v\n", err)
		} else {
			found := false
			for _, topic := range topicList.TopicList {
				if topic == testTopic {
					found = true
					break
				}
			}
			
			if found {
				fmt.Printf("✅ Topic创建验证成功\n")
			} else {
				fmt.Printf("❌ Topic创建验证失败：在列表中未找到\n")
			}
		}
		
		// 4. 清理测试Topic
		if *topicName == "" { // 只有使用测试Topic时才清理
			fmt.Printf("\n4. 清理测试Topic...\n")
			err = mqAdmin.DeleteTopic(ctx, admin.WithTopicDelete(testTopic))
			if err != nil {
				fmt.Printf("⚠ 清理测试Topic失败: %v\n", err)
			} else {
				fmt.Printf("✅ 测试Topic清理成功\n")
			}
		}
	}

	fmt.Printf("\n=== 测试完成 ===\n")
	
	// 5. 诊断建议
	if err != nil {
		fmt.Printf("\n诊断建议:\n")
		fmt.Printf("1. 检查RocketMQ Broker是否正常运行\n")
		fmt.Printf("2. 确认Broker已正确注册到NameServer\n")
		fmt.Printf("3. 检查网络连接和防火墙设置\n")
		fmt.Printf("4. 验证是否有足够的权限创建Topic\n")
		fmt.Printf("5. 查看RocketMQ服务器日志获取更多信息\n")
		
		fmt.Printf("\n检查命令:\n")
		fmt.Printf("# 检查NameServer状态\n")
		fmt.Printf("netstat -tlnp | grep 9876\n")
		fmt.Printf("# 检查Broker状态\n")
		fmt.Printf("netstat -tlnp | grep 10911\n")
		fmt.Printf("# 查看RocketMQ日志\n")
		fmt.Printf("tail -f ~/logs/rocketmqlogs/broker.log\n")
	}
}
