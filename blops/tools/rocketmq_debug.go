package main

import (
	"context"
	"flag"
	"fmt"
	"net"
	"strings"
	"time"

	"github.com/apache/rocketmq-client-go/v2/admin"
	"github.com/apache/rocketmq-client-go/v2/primitive"
)

func main() {
	var (
		nameServer = flag.String("nameserver", "localhost:9876", "RocketMQ NameServer地址")
		username   = flag.String("username", "", "用户名")
		password   = flag.String("password", "", "密码")
		timeout    = flag.Int("timeout", 30, "连接超时时间(秒)")
		debug      = flag.Bool("debug", true, "启用调试模式")
	)
	flag.Parse()

	fmt.Printf("=== RocketMQ 连接诊断工具 ===\n")
	fmt.Printf("NameServer: %s\n", *nameServer)
	fmt.Printf("用户名: %s\n", *username)
	fmt.Printf("超时时间: %d秒\n", *timeout)
	fmt.Printf("调试模式: %t\n", *debug)
	fmt.Printf("========================\n\n")

	// 1. 基础网络连接测试
	fmt.Printf("1. 基础网络连接测试...\n")
	if *nameServer == "" {
		fmt.Printf("❌ NameServer地址为空!\n")
		return
	}

	nameServers := strings.Split(*nameServer, ",")
	for _, ns := range nameServers {
		ns = strings.TrimSpace(ns)
		if ns == "" {
			fmt.Printf("❌ 发现空的NameServer地址\n")
			continue
		}

		fmt.Printf("测试连接到 %s...\n", ns)

		// 检查地址格式
		if !strings.Contains(ns, ":") {
			ns = ns + ":9876" // 默认端口
		}

		conn, err := net.DialTimeout("tcp", ns, time.Duration(*timeout)*time.Second)
		if err != nil {
			fmt.Printf("❌ 网络连接失败: %v\n", err)
			continue
		}
		conn.Close()
		fmt.Printf("✅ 网络连接成功\n")
	}
	fmt.Printf("\n")

	// 2. RocketMQ Admin 连接测试
	fmt.Printf("2. RocketMQ Admin 连接测试...\n")

	// 验证地址格式
	if *nameServer == "" {
		fmt.Printf("❌ NameServer地址为空，无法创建Admin客户端\n")
		return
	}

	fmt.Printf("创建Admin客户端，NameServer: %s\n", *nameServer)

	// 创建Admin客户端
	var mqAdmin admin.Admin
	var err error

	if *username != "" && *password != "" {
		fmt.Printf("使用认证: 用户名=%s\n", *username)
		// 带认证的连接
		mqAdmin, err = admin.NewAdmin(
			admin.WithResolver(primitive.NewPassthroughResolver(strings.Split(*nameServer, ","))),
			admin.WithCredentials(primitive.Credentials{
				AccessKey: *username,
				SecretKey: *password,
			}),
		)
	} else {
		// 不带认证的连接
		mqAdmin, err = admin.NewAdmin(
			admin.WithResolver(primitive.NewPassthroughResolver(strings.Split(*nameServer, ","))),
		)
	}
	if err != nil {
		fmt.Printf("❌ 创建RocketMQ Admin客户端失败: %v\n", err)
		return
	}
	defer mqAdmin.Close()

	fmt.Printf("✅ Admin客户端创建成功\n")

	// 3. 测试获取Topic列表
	fmt.Printf("\n3. 测试获取Topic列表...\n")
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(*timeout)*time.Second)
	defer cancel()

	topicList, err := mqAdmin.FetchAllTopicList(ctx)
	if err != nil {
		fmt.Printf("❌ 获取Topic列表失败: %v\n", err)

		// 详细错误分析
		fmt.Printf("\n错误分析:\n")
		errStr := err.Error()
		if strings.Contains(errStr, "missing address") {
			fmt.Printf("- 地址配置问题：NameServer地址可能为空或格式错误\n")
			fmt.Printf("- 检查地址格式：应为 'host:port' 或 'host1:port1,host2:port2'\n")
		}
		if strings.Contains(errStr, "connection refused") {
			fmt.Printf("- 连接被拒绝：RocketMQ NameServer可能未运行\n")
		}
		if strings.Contains(errStr, "timeout") {
			fmt.Printf("- 连接超时：网络问题或防火墙阻止\n")
		}
		return
	}

	fmt.Printf("✅ 成功获取Topic列表\n")
	fmt.Printf("总Topic数: %d\n", len(topicList.TopicList))

	// 过滤并显示有效的Topic
	validTopics := []string{}
	for _, topic := range topicList.TopicList {
		if !strings.HasPrefix(topic, "%RETRY%") &&
			!strings.HasPrefix(topic, "%DLQ%") &&
			topic != "TBW102" &&
			topic != "OFFSET_MOVED_EVENT" &&
			topic != "SCHEDULE_TOPIC_XXXX" {
			validTopics = append(validTopics, topic)
		}
	}

	fmt.Printf("有效Topic数: %d\n", len(validTopics))

	// 显示前10个有效Topic
	fmt.Printf("前10个有效Topics:\n")
	for i, topic := range validTopics {
		if i >= 10 {
			fmt.Printf("... 还有 %d 个Topics\n", len(validTopics)-10)
			break
		}
		fmt.Printf("  - %s\n", topic)
	}

	// 4. 测试Topic创建功能
	fmt.Printf("\n4. 测试Topic创建功能...\n")
	testTopicName := "test_topic_" + fmt.Sprintf("%d", time.Now().Unix())

	fmt.Printf("尝试创建测试Topic: %s\n", testTopicName)
	err = mqAdmin.CreateTopic(ctx, admin.WithTopicCreate(testTopicName))
	if err != nil {
		fmt.Printf("❌ 创建Topic失败: %v\n", err)

		// 分析创建失败的原因
		errStr := err.Error()
		if strings.Contains(errStr, "missing address") {
			fmt.Printf("- Broker地址缺失：可能是NameServer返回的Broker信息不完整\n")
		}
		if strings.Contains(errStr, "permission") {
			fmt.Printf("- 权限问题：可能需要管理员权限或认证\n")
		}
	} else {
		fmt.Printf("✅ 测试Topic创建成功\n")

		// 清理测试Topic
		fmt.Printf("清理测试Topic...\n")
		err = mqAdmin.DeleteTopic(ctx, admin.WithTopicDelete(testTopicName))
		if err != nil {
			fmt.Printf("⚠ 清理测试Topic失败: %v\n", err)
		} else {
			fmt.Printf("✅ 测试Topic清理成功\n")
		}
	}

	fmt.Printf("\n=== 诊断完成 ===\n")

	// 5. 建议
	fmt.Printf("\n建议检查:\n")
	fmt.Printf("1. 确认RocketMQ NameServer服务状态\n")
	fmt.Printf("2. 确认RocketMQ Broker服务状态\n")
	fmt.Printf("3. 检查网络连接和防火墙设置\n")
	fmt.Printf("4. 验证NameServer地址格式正确\n")
	fmt.Printf("5. 检查是否需要认证配置\n")
}
