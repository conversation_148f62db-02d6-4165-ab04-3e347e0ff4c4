package main

import (
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"../app/clients"
)

func main() {
	var (
		nameServer = flag.String("nameserver", "10.83.0.132:9876", "RocketMQ NameServer地址")
		username   = flag.String("username", "", "用户名")
		password   = flag.String("password", "", "密码")
		testTopic  = flag.String("topic", "", "要测试的Topic名称（留空则使用测试Topic）")
	)
	flag.Parse()

	topic := *testTopic
	if topic == "" {
		topic = fmt.Sprintf("test_operations_%d", time.Now().Unix())
	}

	fmt.Printf("=== RocketMQ 操作对比测试 ===\n")
	fmt.Printf("NameServer: %s\n", *nameServer)
	fmt.Printf("测试Topic: %s\n", topic)
	fmt.Printf("========================\n\n")

	// 创建 RocketMQ 客户端
	client := clients.NewRocketMQClient(*nameServer, *username, *password)
	defer client.Close()

	// 1. 测试连接（这个应该成功）
	fmt.Printf("1. 测试连接...\n")
	err := client.TestConnection()
	if err != nil {
		fmt.Printf("❌ 连接测试失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 连接测试成功\n\n")

	// 2. 测试获取 Topics（这个应该成功）
	fmt.Printf("2. 测试获取Topics...\n")
	topics, err := client.GetTopics()
	if err != nil {
		fmt.Printf("❌ 获取Topics失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 成功获取 %d 个Topics\n", len(topics))

	// 显示前5个Topic
	fmt.Printf("前5个Topics:\n")
	for i, t := range topics {
		if i >= 5 {
			fmt.Printf("... 还有 %d 个Topics\n", len(topics)-5)
			break
		}
		fmt.Printf("  - %s (队列数: %d)\n", t.Name, t.QueueNum)
	}
	fmt.Printf("\n")

	// 3. 测试获取订阅组（这个应该成功）
	fmt.Printf("3. 测试获取订阅组...\n")
	groups, err := client.GetSubscriptionGroups()
	if err != nil {
		fmt.Printf("❌ 获取订阅组失败: %v\n", err)
	} else {
		fmt.Printf("✅ 成功获取 %d 个订阅组\n", len(groups))
		
		// 显示前3个订阅组
		fmt.Printf("前3个订阅组:\n")
		for i, g := range groups {
			if i >= 3 {
				fmt.Printf("... 还有 %d 个订阅组\n", len(groups)-3)
				break
			}
			fmt.Printf("  - %s (状态: %s)\n", g.Name, g.State)
		}
	}
	fmt.Printf("\n")

	// 4. 测试创建Topic（这个可能失败）
	fmt.Printf("4. 测试创建Topic...\n")
	
	// 首先检查Topic是否已存在
	exists := false
	for _, t := range topics {
		if t.Name == topic {
			exists = true
			break
		}
	}

	if exists {
		fmt.Printf("⚠ Topic %s 已存在，跳过创建测试\n", topic)
	} else {
		fmt.Printf("尝试创建Topic: %s\n", topic)
		
		// 使用我们修复后的CreateTopic方法
		err = client.CreateTopic(topic, 4)
		if err != nil {
			fmt.Printf("❌ 创建Topic失败: %v\n", err)
			
			// 分析错误类型
			errStr := err.Error()
			if strings.Contains(errStr, "missing address") {
				fmt.Printf("  - 错误类型: Broker地址缺失\n")
				fmt.Printf("  - 这说明RocketMQ Broker配置有问题\n")
			} else if strings.Contains(errStr, "已存在") {
				fmt.Printf("  - 错误类型: Topic已存在\n")
			} else {
				fmt.Printf("  - 错误类型: 其他错误\n")
			}
		} else {
			fmt.Printf("✅ Topic创建成功!\n")
			
			// 验证创建结果
			fmt.Printf("验证Topic创建...\n")
			time.Sleep(2 * time.Second)
			
			newTopics, err := client.GetTopics()
			if err != nil {
				fmt.Printf("⚠ 无法验证Topic创建结果: %v\n", err)
			} else {
				found := false
				for _, t := range newTopics {
					if t.Name == topic {
						found = true
						break
					}
				}
				
				if found {
					fmt.Printf("✅ Topic创建验证成功\n")
				} else {
					fmt.Printf("❌ Topic创建验证失败：未在列表中找到\n")
				}
			}
			
			// 清理测试Topic（如果是自动生成的）
			if *testTopic == "" {
				fmt.Printf("清理测试Topic...\n")
				// 注意：RocketMQ Go客户端可能不支持删除Topic
				fmt.Printf("⚠ 请手动清理测试Topic: %s\n", topic)
			}
		}
	}

	fmt.Printf("\n=== 测试总结 ===\n")
	fmt.Printf("1. ✅ 连接测试: 成功\n")
	fmt.Printf("2. ✅ 获取Topics: 成功 (%d个)\n", len(topics))
	if len(groups) > 0 {
		fmt.Printf("3. ✅ 获取订阅组: 成功 (%d个)\n", len(groups))
	} else {
		fmt.Printf("3. ⚠ 获取订阅组: 失败或无数据\n")
	}
	
	if exists {
		fmt.Printf("4. ⚠ 创建Topic: 跳过（已存在）\n")
	} else if err != nil {
		fmt.Printf("4. ❌ 创建Topic: 失败\n")
		fmt.Printf("\n这证实了你的观察：获取操作正常，但创建操作失败\n")
		fmt.Printf("问题确实在于创建Topic需要连接到具体的Broker，而获取操作只需要NameServer\n")
	} else {
		fmt.Printf("4. ✅ 创建Topic: 成功\n")
	}

	fmt.Printf("\n建议:\n")
	fmt.Printf("1. 如果创建操作失败，检查RocketMQ Broker配置\n")
	fmt.Printf("2. 确认Broker的brokerIP1参数设置正确\n")
	fmt.Printf("3. 重启Broker服务确保正确注册到NameServer\n")
}
