package main

import (
	"flag"
	"fmt"
	"log"
	"net"
	"strings"
	"time"

	"github.com/IBM/sarama"
)

func main() {
	var (
		address  = flag.String("address", "***********:9092", "Kafka broker地址")
		username = flag.String("username", "", "用户名")
		password = flag.String("password", "", "密码")
		timeout  = flag.Int("timeout", 30, "连接超时时间(秒)")
		debug    = flag.Bool("debug", true, "启用调试模式")
	)
	flag.Parse()

	fmt.Printf("=== Kafka 连接诊断工具 ===\n")
	fmt.Printf("地址: %s\n", *address)
	fmt.Printf("用户名: %s\n", *username)
	fmt.Printf("超时时间: %d秒\n", *timeout)
	fmt.Printf("调试模式: %t\n", *debug)
	fmt.Printf("========================\n\n")

	// 1. 基础网络连接测试
	fmt.Printf("1. 基础网络连接测试...\n")
	brokers := strings.Split(*address, ",")
	for _, broker := range brokers {
		fmt.Printf("测试连接到 %s...\n", broker)
		conn, err := net.DialTimeout("tcp", broker, time.Duration(*timeout)*time.Second)
		if err != nil {
			fmt.Printf("❌ 网络连接失败: %v\n", err)
			continue
		}
		conn.Close()
		fmt.Printf("✅ 网络连接成功\n")
	}
	fmt.Printf("\n")

	// 2. Kafka 协议连接测试
	fmt.Printf("2. Kafka 协议连接测试...\n")
	
	// 启用调试日志
	if *debug {
		sarama.Logger = log.New(log.Writer(), "[SARAMA] ", log.LstdFlags)
	}

	// 创建配置
	config := sarama.NewConfig()
	config.Net.DialTimeout = time.Duration(*timeout) * time.Second
	config.Net.ReadTimeout = time.Duration(*timeout) * time.Second
	config.Net.WriteTimeout = time.Duration(*timeout) * time.Second
	
	// 设置更宽松的连接参数
	config.Net.KeepAlive = 30 * time.Second
	config.Metadata.Retry.Max = 3
	config.Metadata.Retry.Backoff = 250 * time.Millisecond
	config.Metadata.RefreshFrequency = 10 * time.Minute
	config.Metadata.Full = true
	
	// 尝试不同的版本
	versions := []sarama.KafkaVersion{
		sarama.V2_6_0_0,
		sarama.V2_4_0_0,
		sarama.V2_0_0_0,
		sarama.V1_0_0_0,
		sarama.V0_10_2_0,
	}

	for _, version := range versions {
		fmt.Printf("尝试 Kafka 版本: %s\n", version.String())
		config.Version = version
		
		if *username != "" && *password != "" {
			config.Net.SASL.Enable = true
			config.Net.SASL.Mechanism = sarama.SASLTypePlaintext
			config.Net.SASL.User = *username
			config.Net.SASL.Password = *password
		}

		// 测试连接
		admin, err := sarama.NewClusterAdmin(brokers, config)
		if err != nil {
			fmt.Printf("❌ 版本 %s 连接失败: %v\n", version.String(), err)
			continue
		}

		// 尝试获取元数据
		topics, err := admin.ListTopics()
		admin.Close()
		
		if err != nil {
			fmt.Printf("❌ 版本 %s 获取元数据失败: %v\n", version.String(), err)
			continue
		}

		fmt.Printf("✅ 版本 %s 连接成功! 找到 %d 个 topics\n", version.String(), len(topics))
		
		// 显示前5个topic
		count := 0
		for topicName := range topics {
			if !strings.HasPrefix(topicName, "__") {
				fmt.Printf("  - %s\n", topicName)
				count++
				if count >= 5 {
					break
				}
			}
		}
		return
	}

	fmt.Printf("❌ 所有版本都连接失败\n")

	// 3. 详细错误分析
	fmt.Printf("\n3. 详细错误分析...\n")
	fmt.Printf("可能的问题:\n")
	fmt.Printf("1. Kafka 服务器未运行或不可访问\n")
	fmt.Printf("2. 防火墙阻止连接\n")
	fmt.Printf("3. Kafka 版本不兼容\n")
	fmt.Printf("4. 认证配置错误\n")
	fmt.Printf("5. 网络配置问题\n")
	
	fmt.Printf("\n建议检查:\n")
	fmt.Printf("1. 确认 Kafka 服务器状态: systemctl status kafka\n")
	fmt.Printf("2. 检查端口是否开放: netstat -tlnp | grep 9092\n")
	fmt.Printf("3. 检查防火墙设置\n")
	fmt.Printf("4. 查看 Kafka 服务器日志\n")
}
