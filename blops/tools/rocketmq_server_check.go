package main

import (
	"context"
	"flag"
	"fmt"
	"net"
	"strings"
	"time"

	"github.com/apache/rocketmq-client-go/v2/admin"
	"github.com/apache/rocketmq-client-go/v2/primitive"
)

func main() {
	var (
		nameServer = flag.String("nameserver", "10.83.0.132:9876", "RocketMQ NameServer地址")
		username   = flag.String("username", "", "用户名")
		password   = flag.String("password", "", "密码")
		timeout    = flag.Int("timeout", 30, "连接超时时间(秒)")
	)
	flag.Parse()

	fmt.Printf("=== RocketMQ 服务器状态检查 ===\n")
	fmt.Printf("NameServer: %s\n", *nameServer)
	fmt.Printf("超时时间: %d秒\n", *timeout)
	fmt.Printf("========================\n\n")

	// 1. 检查 NameServer 网络连接
	fmt.Printf("1. 检查 NameServer 网络连接...\n")
	nameServers := strings.Split(*nameServer, ",")
	for _, ns := range nameServers {
		ns = strings.TrimSpace(ns)
		if ns == "" {
			continue
		}

		fmt.Printf("测试连接到 NameServer: %s\n", ns)
		conn, err := net.DialTimeout("tcp", ns, time.Duration(*timeout)*time.Second)
		if err != nil {
			fmt.Printf("❌ NameServer %s 连接失败: %v\n", ns, err)
			continue
		}
		conn.Close()
		fmt.Printf("✅ NameServer %s 连接成功\n", ns)
	}

	// 2. 检查可能的 Broker 端口
	fmt.Printf("\n2. 检查可能的 Broker 端口...\n")
	brokerPorts := []string{"10909", "10911", "10912"}
	
	for _, ns := range nameServers {
		ns = strings.TrimSpace(ns)
		if ns == "" || !strings.Contains(ns, ":") {
			continue
		}
		
		host := strings.Split(ns, ":")[0]
		fmt.Printf("检查主机 %s 的 Broker 端口:\n", host)
		
		for _, port := range brokerPorts {
			brokerAddr := host + ":" + port
			fmt.Printf("  测试 %s...", brokerAddr)
			
			conn, err := net.DialTimeout("tcp", brokerAddr, 5*time.Second)
			if err != nil {
				fmt.Printf(" ❌ 无法连接\n")
			} else {
				conn.Close()
				fmt.Printf(" ✅ 连接成功\n")
			}
		}
	}

	// 3. 创建 Admin 客户端并获取详细信息
	fmt.Printf("\n3. 创建 Admin 客户端...\n")
	var mqAdmin admin.Admin
	var err error

	if *username != "" && *password != "" {
		mqAdmin, err = admin.NewAdmin(
			admin.WithResolver(primitive.NewPassthroughResolver(nameServers)),
			admin.WithCredentials(primitive.Credentials{
				AccessKey: *username,
				SecretKey: *password,
			}),
		)
	} else {
		mqAdmin, err = admin.NewAdmin(
			admin.WithResolver(primitive.NewPassthroughResolver(nameServers)),
		)
	}

	if err != nil {
		fmt.Printf("❌ 创建Admin客户端失败: %v\n", err)
		return
	}
	defer mqAdmin.Close()

	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(*timeout)*time.Second)
	defer cancel()

	fmt.Printf("✅ Admin客户端创建成功\n")

	// 4. 获取 Topic 列表
	fmt.Printf("\n4. 获取 Topic 列表...\n")
	topicList, err := mqAdmin.FetchAllTopicList(ctx)
	if err != nil {
		fmt.Printf("❌ 获取Topic列表失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 成功获取 %d 个Topics\n", len(topicList.TopicList))

	// 5. 尝试获取集群信息（如果API支持）
	fmt.Printf("\n5. 尝试获取更多服务器信息...\n")
	
	// 由于 rocketmq-client-go 的 API 限制，我们无法直接获取 Broker 信息
	// 但我们可以尝试一些其他方法
	
	fmt.Printf("由于 RocketMQ Go 客户端 API 限制，无法直接获取 Broker 集群信息\n")
	fmt.Printf("建议在 RocketMQ 服务器上执行以下命令检查状态:\n\n")
	
	fmt.Printf("# 检查 NameServer 状态\n")
	fmt.Printf("sh mqnamesrv -c /path/to/namesrv.properties\n")
	fmt.Printf("netstat -tlnp | grep 9876\n\n")
	
	fmt.Printf("# 检查 Broker 状态\n")
	fmt.Printf("sh mqbroker -c /path/to/broker.properties\n")
	fmt.Printf("netstat -tlnp | grep 10911\n\n")
	
	fmt.Printf("# 查看 Broker 是否注册到 NameServer\n")
	fmt.Printf("sh mqadmin clusterList -n %s\n", *nameServer)
	fmt.Printf("sh mqadmin brokerStatus -n %s -b broker-name\n", *nameServer)

	// 6. 分析问题
	fmt.Printf("\n6. 问题分析...\n")
	fmt.Printf("根据 'dial tcp: missing address' 错误，可能的原因:\n")
	fmt.Printf("1. ✅ NameServer 连接正常\n")
	fmt.Printf("2. ❌ Broker 没有正确注册到 NameServer\n")
	fmt.Printf("3. ❌ Broker 服务未启动或配置错误\n")
	fmt.Printf("4. ❌ NameServer 返回的 Broker 地址为空\n")
	fmt.Printf("5. ❌ 网络配置问题（如 Broker 绑定了内网地址但客户端无法访问）\n")

	fmt.Printf("\n建议解决步骤:\n")
	fmt.Printf("1. 检查 RocketMQ Broker 服务是否正在运行\n")
	fmt.Printf("2. 检查 Broker 配置文件中的 namesrvAddr 设置\n")
	fmt.Printf("3. 检查 Broker 配置文件中的 brokerIP1 设置\n")
	fmt.Printf("4. 重启 Broker 服务确保正确注册到 NameServer\n")
	fmt.Printf("5. 使用 RocketMQ 管理工具检查集群状态\n")

	fmt.Printf("\n=== 检查完成 ===\n")
}
