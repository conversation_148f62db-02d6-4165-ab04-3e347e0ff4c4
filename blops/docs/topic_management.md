# Topic管理功能文档

## 功能概述

Topic管理功能允许用户配置和管理Kafka和RocketMQ的连接，并查看对应的Topic和Group信息。

## 功能特性

### 1. Topic配置管理
- ✅ 支持添加、编辑、删除Topic配置
- ✅ 支持Kafka和RocketMQ两种类型
- ✅ 与环境管理集成，支持按环境分类
- ✅ 连接测试功能，验证配置有效性

### 2. 实时数据获取
- ✅ **Kafka集成**：获取Topic列表（包括分区数、副本数）
- ✅ **RocketMQ集成**：获取Topic列表和SubscriptionGroup信息
- ✅ 错误处理和超时机制
- ✅ 连接池管理，避免资源泄露

### 3. 用户界面
- ✅ 直观的表格展示和操作
- ✅ 模态框形式的配置编辑
- ✅ 响应式的Topic/Group信息查看
- ✅ 加载状态指示和错误提示

## API接口

### Topic配置管理
```
GET    /api/v1/topic/config                    # 获取配置列表
GET    /api/v1/topic/config/:id                # 获取单个配置
POST   /api/v1/topic/config                    # 创建配置
PUT    /api/v1/topic/config                    # 更新配置
DELETE /api/v1/topic/config/:id                # 删除配置
POST   /api/v1/topic/config/test-connection    # 测试连接
```

### Topic和Group查询
```
GET    /api/v1/topic/config/:id/topics-groups  # 获取Topic和Group信息
```

### 过滤查询
```
POST   /api/v1/topic/config/by-type            # 按类型过滤
POST   /api/v1/topic/config/by-env             # 按环境过滤
POST   /api/v1/topic/config/by-type-env        # 按类型和环境过滤
```

## 数据库表结构

```sql
CREATE TABLE `t_topic_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `env` bigint(20) NOT NULL COMMENT '所属环境ID',
  `name` varchar(255) NOT NULL COMMENT '配置名称',
  `type` varchar(50) NOT NULL COMMENT '类型：kafka 或 rocketmq',
  `address` varchar(500) NOT NULL COMMENT '连接地址',
  `username` varchar(255) DEFAULT NULL COMMENT '用户名（可选）',
  `password` varchar(255) DEFAULT NULL COMMENT '密码（可选）',
  `description` text COMMENT '描述信息',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_env` (`env`),
  KEY `idx_type` (`type`),
  KEY `idx_name` (`name`),
  CONSTRAINT `fk_topic_config_env` FOREIGN KEY (`env`) REFERENCES `t_middleware_env` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Topic配置表';
```

## 使用说明

### 1. 配置Kafka连接
1. 进入"中间件" -> "topic管理"页面
2. 点击"添加配置"按钮
3. 填写配置信息：
   - 环境：选择对应的环境
   - 配置名称：自定义名称
   - 类型：选择"Kafka"
   - 连接地址：格式为 `host:port` 或 `host1:port1,host2:port2`
   - 用户名/密码：可选
4. 点击"测试连接"验证配置
5. 点击"创建"保存配置

### 2. 配置RocketMQ连接
1. 进入"中间件" -> "topic管理"页面
2. 点击"添加配置"按钮
3. 填写配置信息：
   - 环境：选择对应的环境
   - 配置名称：自定义名称
   - 类型：选择"RocketMQ"
   - 连接地址：NameServer地址，格式为 `host:port`
   - 用户名/密码：可选
4. 点击"测试连接"验证配置
5. 点击"创建"保存配置

### 3. 查看Topic和Group信息
1. 在配置列表中找到目标配置
2. 点击"查看Topics"按钮
3. 系统会连接到对应的服务器获取实时数据
4. 查看Topic列表和Group/SubscriptionGroup列表
5. 可以点击"刷新"按钮获取最新数据

## 技术实现

### 后端架构
- **客户端层**：`app/clients/` - Kafka和RocketMQ客户端封装
- **服务层**：`app/services/` - 业务逻辑处理
- **控制器层**：`app/controllers/` - HTTP请求处理
- **数据层**：`app/models/` - 数据模型和数据库操作

### 依赖库
- **Kafka客户端**：`github.com/segmentio/kafka-go`
- **RocketMQ客户端**：`github.com/apache/rocketmq-client-go/v2`

### 前端组件
- **主组件**：`blops-web/src/pages/middleware/TopicManager.jsx`
- **路由配置**：`blops-web/config/routes.ts`

## 故障排除

### 常见问题

1. **连接测试失败**
   - 检查服务器地址是否正确
   - 确认服务器是否运行
   - 检查网络连接和防火墙设置

2. **获取Topic列表为空**
   - 确认服务器上确实存在Topic
   - 检查用户权限（如果配置了认证）
   - 查看服务器日志

3. **RocketMQ连接问题**
   - 确认NameServer地址正确
   - 检查Broker是否正常运行
   - 验证端口配置（NameServer通常是9876，Broker通常是10911）

### 日志查看
- 后端日志：查看Go应用日志
- 前端错误：打开浏览器开发者工具查看Console

## 扩展开发

### 添加新的消息队列类型
1. 在 `app/clients/` 目录下创建新的客户端文件
2. 实现相应的接口方法
3. 在 `middleware_service_impl.go` 中添加新的处理逻辑
4. 更新前端类型选择器

### 增强功能
- 添加Topic创建/删除功能
- 支持消息发送/接收测试
- 添加性能监控指标
- 支持更多认证方式
