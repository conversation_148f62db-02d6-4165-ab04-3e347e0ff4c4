# RocketMQ "missing address" 错误修复方案

## 问题描述

用户在使用 RocketMQ Topic 同步功能时遇到连接问题：

**错误信息**：
```
ERRO[1002] create topic error broker= topic=biz_sale_material_requirements_batch_project_message_test underlayError="dial tcp: missing address"
```

**关键问题**：
- `broker=` 显示为空值
- 错误信息 "dial tcp: missing address" 表明地址配置缺失

## 根本原因分析

通过代码分析发现问题出现在以下几个方面：

### 1. 数据库配置问题
- RocketMQ 配置的 `address` 字段可能为空或格式错误
- 缺少端口号（默认应为 9876）
- 地址格式不规范（如多余的逗号）

### 2. 客户端验证不足
- 创建客户端时没有验证 NameServer 地址
- 缺少地址标准化处理
- 没有重试机制

### 3. 错误处理不完善
- 错误信息不够详细
- 缺少地址验证的早期检查

## 修复方案

### 1. 客户端代码优化

**文件**: `app/clients/rocketmq_client.go`

主要改进：
- ✅ 添加地址验证和标准化函数
- ✅ 增强连接错误处理
- ✅ 添加重试机制
- ✅ 增加超时时间到 30 秒

```go
// 关键修复
func validateAndNormalizeNameServerAddr(nameServerAddr string) string {
    if nameServerAddr == "" {
        return ""
    }
    
    nameServers := strings.Split(nameServerAddr, ",")
    var validNameServers []string
    
    for _, ns := range nameServers {
        ns = strings.TrimSpace(ns)
        if ns == "" {
            continue
        }
        
        // 如果没有端口，添加默认端口9876
        if !strings.Contains(ns, ":") {
            ns = ns + ":9876"
        }
        
        validNameServers = append(validNameServers, ns)
    }
    
    return strings.Join(validNameServers, ",")
}
```

### 2. 数据库配置修复

**执行 SQL 脚本**: `scripts/fix_rocketmq_config.sql`

主要修复：
- ✅ 检查并修复空地址问题
- ✅ 为缺少端口的地址添加默认端口 9876
- ✅ 清理地址格式中的多余逗号
- ✅ 重置失败的缓存和同步任务

```sql
-- 修复空地址
UPDATE t_topic_config 
SET address = 'localhost:9876'
WHERE type = 'rocketmq' AND (address IS NULL OR address = '');

-- 添加默认端口
UPDATE t_topic_config 
SET address = CONCAT(address, ':9876')
WHERE type = 'rocketmq' AND address NOT LIKE '%:%';
```

### 3. 诊断工具

**文件**: `tools/rocketmq_debug.go`

功能：
- ✅ 检查 NameServer 地址格式
- ✅ 测试网络连接
- ✅ 验证 RocketMQ Admin 连接
- ✅ 测试 Topic 操作功能

## 部署步骤

### 步骤 1：修复数据库配置

```bash
# 方法1：直接执行 SQL（推荐）
# 将 scripts/fix_rocketmq_config.sql 中的 SQL 语句在数据库中执行

# 方法2：通过管理界面
# 1. 登录应用管理界面
# 2. 进入"中间件" -> "Topic管理"
# 3. 检查 RocketMQ 配置的地址字段
# 4. 确保格式为：host:port 或 host1:port1,host2:port2
# 5. 如果缺少端口，添加 :9876
```

### 步骤 2：验证配置

```bash
# 使用诊断工具验证
cd tools
go run rocketmq_debug.go -nameserver="your_nameserver:9876"

# 应该看到：
# ✅ 网络连接成功
# ✅ Admin客户端创建成功
# ✅ 成功获取Topic列表
```

### 步骤 3：重启应用

```bash
# 停止应用
systemctl stop blops

# 部署新版本
# （将编译好的 blops 二进制文件复制到服务器）

# 启动应用
systemctl start blops

# 检查状态
systemctl status blops
```

### 步骤 4：测试修复

```bash
# 1. 检查应用日志
journalctl -u blops -f

# 2. 测试 Topic 同步功能
# 通过管理界面或 API 测试 RocketMQ Topic 同步

# 3. 验证不再出现 "missing address" 错误
```

## 常见 RocketMQ 地址格式

### 正确格式：
- `localhost:9876`
- `*************:9876`
- `rocketmq1:9876,rocketmq2:9876`
- `***********:9876`

### 错误格式：
- ❌ 空字符串 `""`
- ❌ 只有主机名 `"localhost"`
- ❌ 多余逗号 `"host1:9876,"`
- ❌ 前导逗号 `",host1:9876"`

## 故障排除

### 如果问题仍然存在：

1. **检查数据库配置**：
   ```sql
   SELECT id, name, type, address FROM t_topic_config WHERE type = 'rocketmq';
   ```

2. **使用诊断工具**：
   ```bash
   cd tools
   go run rocketmq_debug.go -nameserver="your_actual_nameserver:9876"
   ```

3. **检查 RocketMQ 服务状态**：
   ```bash
   # 在 RocketMQ 服务器上
   netstat -tlnp | grep 9876  # NameServer
   netstat -tlnp | grep 10911 # Broker
   ```

4. **检查网络连接**：
   ```bash
   telnet your_nameserver_host 9876
   ```

## 预期效果

修复后应该看到：
- ✅ 不再出现 "dial tcp: missing address" 错误
- ✅ RocketMQ Topic 同步功能正常工作
- ✅ 日志中显示正确的 broker 地址
- ✅ Topic 创建和管理操作成功

## 总结

这次修复的核心是：
- **识别了地址配置缺失的根本问题**
- **添加了完善的地址验证和标准化机制**
- **提供了数据库配置修复脚本**
- **创建了诊断工具帮助排查问题**

修复后，RocketMQ 连接应该稳定，Topic 同步功能应该正常工作。
