# Kafka 集群连接问题修复方案

## 问题根因

通过诊断工具发现，真正的问题是：

1. **Kafka 集群配置不完整**：数据库中只配置了单个 broker 地址 `***********:9092`
2. **实际 Kafka 集群有 3 个 broker**：
   - ***********:9092 (broker #1)
   - ***********:9092 (broker #2) 
   - ***********:9092 (broker #3)
3. **客户端配置不够健壮**：缺少必要的重试和容错机制

## 诊断结果

```bash
# 单个 broker 测试
✅ 单个broker连接成功! - Topics: 97 个, 消费者组: 17 个

# 集群测试  
✅ 集群连接成功! - Topics: 97 个, 消费者组: 17 个
```

## 修复方案

### 1. 更新数据库配置

**执行 SQL 脚本**：`scripts/update_kafka_config.sql`

```sql
-- 更新 Kafka 配置为完整集群地址
UPDATE t_topic_config 
SET address = '***********:9092,***********:9092,***********:9092',
    description = CONCAT(IFNULL(description, ''), ' - 已更新为完整集群地址'),
    updated_at = NOW()
WHERE id = 1 AND type = 'kafka';

-- 清理失败的缓存
UPDATE t_topic_group_cache_meta 
SET sync_status = 'pending', error_message = NULL, updated_at = NOW()
WHERE config_id = 1;

DELETE FROM t_topic_cache WHERE config_id = 1;
DELETE FROM t_group_cache WHERE config_id = 1;
```

### 2. 客户端配置优化

**文件**：`app/clients/kafka_sarama_client.go`

主要改进：
- ✅ 增加客户端ID设置
- ✅ 优化消费者和生产者配置
- ✅ 增强错误处理和重试机制
- ✅ 设置合适的超时参数

```go
// 关键配置
config.ClientID = "blops-kafka-client"
config.Version = sarama.V2_6_0_0
config.Net.DialTimeout = 30 * time.Second
config.Metadata.Retry.Max = 3
config.Consumer.Return.Errors = true
config.Producer.Return.Successes = true
```

### 3. 重试机制增强

所有 Kafka 操作都添加了 3 次重试机制：
- TestConnection()
- GetTopics()
- GetConsumerGroups()

## 部署步骤

### 步骤 1：更新数据库配置

```bash
# 方法1：如果有 mysql 客户端
mysql -h *********** -P 3306 -u blops_admin_user -p blops_admin < scripts/update_kafka_config.sql

# 方法2：通过应用管理界面
# 1. 登录应用管理界面
# 2. 进入"中间件" -> "Topic管理"
# 3. 编辑配置ID为1的Kafka配置
# 4. 将地址更新为：***********:9092,***********:9092,***********:9092
# 5. 保存配置
```

### 步骤 2：重启应用

```bash
# 停止当前应用
systemctl stop blops

# 备份当前版本（可选）
cp blops blops.backup.$(date +%Y%m%d_%H%M%S)

# 部署新版本
# （将编译好的 blops 二进制文件复制到服务器）

# 启动应用
systemctl start blops

# 检查状态
systemctl status blops
```

### 步骤 3：验证修复

```bash
# 1. 检查应用日志
journalctl -u blops -f

# 2. 测试 API 接口
curl -X GET "http://localhost:3000/api/v1/topic/config/1/topics-groups"

# 3. 检查响应时间和错误率
# 应该看到：
# - 不再有 500 错误
# - 响应时间 < 5 秒
# - 成功获取 Topics 和消费者组信息
```

## 监控建议

### 1. 应用日志监控

关注以下日志：
```
[INFO] 开始连接Kafka集群: ***********:9092,***********:9092,***********:9092
[INFO] Kafka连接测试成功
[INFO] 成功获取到 XX 个Kafka Topics
[INFO] 成功获取到 XX 个消费者组
```

### 2. 错误监控

如果仍有问题，检查：
```
[ERROR] Kafka连接测试失败
[ERROR] 获取Kafka Topic列表失败
[ERROR] 获取消费者组失败
```

### 3. 性能监控

- API 响应时间应 < 5 秒
- 数据库查询时间应 < 200ms
- 成功率应 > 95%

## 故障排除

如果问题仍然存在：

1. **检查网络连接**：
   ```bash
   telnet *********** 9092
   telnet *********** 9092  
   telnet *********** 9092
   ```

2. **检查 Kafka 集群状态**：
   ```bash
   # 在 Kafka 服务器上执行
   systemctl status kafka
   netstat -tlnp | grep 9092
   ```

3. **使用诊断工具**：
   ```bash
   cd tools
   go run kafka_debug.go -address="***********:9092,***********:9092,***********:9092"
   ```

4. **检查防火墙和安全组**：
   确保应用服务器可以访问所有 3 个 Kafka broker 的 9092 端口

## 总结

这次修复的核心是：
- **发现了 Kafka 集群配置不完整的根本问题**
- **通过诊断工具确认了集群的真实拓扑**
- **更新配置以包含所有 broker 地址**
- **增强了客户端的容错能力**

修复后，应用应该能够稳定连接到 Kafka 集群并正常获取 Topics 和消费者组信息。
