-- 修复 RocketMQ 配置问题
-- 解决 "dial tcp: missing address" 错误

-- 1. 查看当前所有配置
SELECT id, name, type, address, username, description, created_at, updated_at
FROM t_topic_config 
ORDER BY type, id;

-- 2. 查看 RocketMQ 配置
SELECT id, name, type, address, username, description
FROM t_topic_config 
WHERE type = 'rocketmq';

-- 3. 检查是否有空地址或格式错误的配置
SELECT id, name, type, address, 
       CASE 
         WHEN address IS NULL OR address = '' THEN 'ADDRESS_EMPTY'
         WHEN address NOT LIKE '%:%' THEN 'MISSING_PORT'
         WHEN address LIKE '%,%' THEN 'TRAILING_COMMA'
         WHEN address LIKE ',%' THEN 'LEADING_COMMA'
         ELSE 'OK'
       END as address_status
FROM t_topic_config 
WHERE type = 'rocketmq';

-- 4. 修复空地址问题（如果存在）
UPDATE t_topic_config 
SET address = 'localhost:9876',
    description = CONCAT(IFNULL(description, ''), ' - 已修复空地址问题'),
    updated_at = NOW()
WHERE type = 'rocketmq' 
  AND (address IS NULL OR address = '');

-- 5. 修复缺少端口的地址
UPDATE t_topic_config 
SET address = CONCAT(address, ':9876'),
    description = CONCAT(IFNULL(description, ''), ' - 已添加默认端口9876'),
    updated_at = NOW()
WHERE type = 'rocketmq' 
  AND address NOT LIKE '%:%'
  AND address IS NOT NULL 
  AND address != '';

-- 6. 清理地址中的多余逗号
UPDATE t_topic_config 
SET address = TRIM(BOTH ',' FROM address),
    description = CONCAT(IFNULL(description, ''), ' - 已清理地址格式'),
    updated_at = NOW()
WHERE type = 'rocketmq' 
  AND (address LIKE '%,%' OR address LIKE ',%');

-- 7. 验证修复结果
SELECT id, name, type, address, username, description, updated_at
FROM t_topic_config 
WHERE type = 'rocketmq';

-- 8. 清理相关的失败缓存记录
UPDATE t_topic_group_cache_meta 
SET sync_status = 'pending', 
    error_message = NULL,
    updated_at = NOW()
WHERE config_id IN (SELECT id FROM t_topic_config WHERE type = 'rocketmq');

-- 9. 删除旧的缓存数据，强制重新获取
DELETE FROM t_topic_cache 
WHERE config_id IN (SELECT id FROM t_topic_config WHERE type = 'rocketmq');

DELETE FROM t_group_cache 
WHERE config_id IN (SELECT id FROM t_topic_config WHERE type = 'rocketmq');

-- 10. 如果需要，插入一个示例 RocketMQ 配置
-- INSERT INTO t_topic_config (env, name, type, address, username, password, description)
-- VALUES (1, 'RocketMQ测试环境', 'rocketmq', 'localhost:9876', '', '', 'RocketMQ测试环境配置 - 自动创建');

-- 11. 检查同步任务中是否有相关的失败记录
SELECT id, name, type, source_config_id, target_config_id, status, error_message
FROM t_sync_task 
WHERE type = 'rocketmq' 
  AND status = 'failed'
ORDER BY updated_at DESC;

-- 12. 重置失败的同步任务状态
UPDATE t_sync_task 
SET status = 'pending',
    error_message = NULL,
    progress = 0,
    completed_items = 0,
    failed_items = 0,
    updated_at = NOW()
WHERE type = 'rocketmq' 
  AND status = 'failed';

-- 13. 最终验证查询
SELECT 
  tc.id,
  tc.name,
  tc.type,
  tc.address,
  tc.username,
  tc.description,
  tgcm.sync_status,
  tgcm.error_message,
  tgcm.last_sync_at
FROM t_topic_config tc
LEFT JOIN t_topic_group_cache_meta tgcm ON tc.id = tgcm.config_id
WHERE tc.type = 'rocketmq'
ORDER BY tc.id;
