#!/bin/bash

# Kafka连接问题修复脚本
# 用于诊断和修复Kafka连接相关问题

set -e

echo "=== Kafka问题诊断和修复脚本 ==="
echo "时间: $(date)"
echo "================================"

# 配置
KAFKA_HOST="***********"
KAFKA_PORT="9092"
DB_HOST="***********"
DB_PORT="3306"
DB_NAME="blops_admin"
DB_USER="blops_admin_user"

# 1. 检查网络连接
echo "1. 检查网络连接..."
if timeout 5 bash -c "</dev/tcp/$KAFKA_HOST/$KAFKA_PORT"; then
    echo "✓ Kafka服务器 $KAFKA_HOST:$KAFKA_PORT 网络连接正常"
else
    echo "✗ Kafka服务器 $KAFKA_HOST:$KAFKA_PORT 网络连接失败"
    echo "请检查:"
    echo "  - 服务器是否运行"
    echo "  - 防火墙设置"
    echo "  - 网络连接"
    exit 1
fi

# 2. 检查数据库连接
echo "2. 检查数据库连接..."
if timeout 5 bash -c "</dev/tcp/$DB_HOST/$DB_PORT"; then
    echo "✓ 数据库服务器 $DB_HOST:$DB_PORT 网络连接正常"
else
    echo "✗ 数据库服务器 $DB_HOST:$DB_PORT 网络连接失败"
    exit 1
fi

# 3. 运行数据库索引优化
echo "3. 优化数据库索引..."
if [ -f "migration/optimize_topic_cache_indexes.sql" ]; then
    echo "执行索引优化SQL..."
    # 这里需要根据实际情况调整数据库连接命令
    # mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASSWORD $DB_NAME < migration/optimize_topic_cache_indexes.sql
    echo "✓ 请手动执行 migration/optimize_topic_cache_indexes.sql 来优化数据库索引"
else
    echo "⚠ 索引优化文件不存在"
fi

# 4. 清理失败的缓存记录
echo "4. 清理失败的缓存记录..."
echo "请在数据库中执行以下SQL来清理失败的缓存:"
echo "UPDATE t_topic_group_cache_meta SET sync_status = 'pending' WHERE sync_status = 'failed';"
echo "DELETE FROM t_topic_cache WHERE config_id IN (SELECT config_id FROM t_topic_group_cache_meta WHERE sync_status = 'failed');"
echo "DELETE FROM t_group_cache WHERE config_id IN (SELECT config_id FROM t_topic_group_cache_meta WHERE sync_status = 'failed');"

# 5. 测试Kafka连接
echo "5. 测试Kafka连接..."
if [ -f "tools/kafka_test.go" ]; then
    echo "编译并运行Kafka测试工具..."
    cd tools
    go build -o kafka_test kafka_test.go
    if [ -f "kafka_test" ]; then
        ./kafka_test -address="$KAFKA_HOST:$KAFKA_PORT"
        echo "✓ Kafka连接测试完成"
    else
        echo "⚠ Kafka测试工具编译失败"
    fi
    cd ..
else
    echo "⚠ Kafka测试工具不存在"
fi

# 6. 重启建议
echo "6. 重启建议..."
echo "如果问题仍然存在，建议:"
echo "  1. 重启应用服务: systemctl restart blops"
echo "  2. 检查应用日志: journalctl -u blops -f"
echo "  3. 监控数据库慢查询日志"
echo "  4. 检查Kafka服务器状态"

echo ""
echo "=== 修复脚本执行完成 ==="
echo "时间: $(date)"
