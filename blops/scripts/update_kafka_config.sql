-- 更新 Kafka 配置以包含所有 broker 地址
-- 根据诊断结果，Kafka 集群有 3 个 broker

-- 查看当前配置
SELECT id, name, type, address, username, description 
FROM t_topic_config 
WHERE type = 'kafka';

-- 更新配置ID为1的Kafka地址，包含所有broker
UPDATE t_topic_config 
SET address = '***********:9092,***********:9092,***********:9092',
    description = CONCAT(IFNULL(description, ''), ' - 已更新为完整集群地址'),
    updated_at = NOW()
WHERE id = 1 AND type = 'kafka';

-- 验证更新结果
SELECT id, name, type, address, username, description, updated_at
FROM t_topic_config 
WHERE id = 1;

-- 清理失败的缓存记录
UPDATE t_topic_group_cache_meta 
SET sync_status = 'pending', 
    error_message = NULL,
    updated_at = NOW()
WHERE config_id = 1;

-- 删除旧的缓存数据，强制重新获取
DELETE FROM t_topic_cache WHERE config_id = 1;
DELETE FROM t_group_cache WHERE config_id = 1;
