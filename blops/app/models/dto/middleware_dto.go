package dto

import (
	"blops/app/models/po"
	"time"
)

// 环境相关DTO

// MiddlewareEnvCreateRequest 创建环境请求
type MiddlewareEnvCreateRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
}

// MiddlewareEnvUpdateRequest 更新环境请求
type MiddlewareEnvUpdateRequest struct {
	ID          int64  `json:"id" binding:"required"`
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
}

// MiddlewareEnvResponse 环境响应
type MiddlewareEnvResponse struct {
	ID          int64     `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

// 链接相关DTO

// MiddlewareLinkCreateRequest 创建链接请求
type MiddlewareLinkCreateRequest struct {
	Env         int64  `json:"env" binding:"required"`
	Name        string `json:"name" binding:"required"`
	Type        string `json:"type" binding:"required"`
	URL         string `json:"url" binding:"required"`
	Description string `json:"description"`
}

// MiddlewareLinkUpdateRequest 更新链接请求
type MiddlewareLinkUpdateRequest struct {
	ID          int64  `json:"id" binding:"required"`
	Env         int64  `json:"env" binding:"required"`
	Name        string `json:"name" binding:"required"`
	Type        string `json:"type" binding:"required"`
	URL         string `json:"url" binding:"required"`
	Description string `json:"description"`
}

// MiddlewareLinkResponse 链接响应
type MiddlewareLinkResponse struct {
	ID          int64                  `json:"id"`
	Env         int64                  `json:"env"`
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`
	URL         string                 `json:"url"`
	Description string                 `json:"description"`
	TopicCount  *int                   `json:"topicCount,omitempty"`  // Topic数量
	GroupCount  *int                   `json:"groupCount,omitempty"`  // Group数量
	CreatedAt   time.Time              `json:"createdAt"`
	UpdatedAt   time.Time              `json:"updatedAt"`
	EnvInfo     *MiddlewareEnvResponse `json:"envInfo,omitempty"`
}

// MiddlewareLinkTypeFilterRequest 按类型过滤链接请求
type MiddlewareLinkTypeFilterRequest struct {
	Type string `json:"type" binding:"required"`
}

// MiddlewareLinkEnvFilterRequest 按环境过滤链接请求
type MiddlewareLinkEnvFilterRequest struct {
	Env int64 `json:"env" binding:"required"`
}

// MiddlewareLinkTypeAndEnvFilterRequest 按类型和环境过滤链接请求
type MiddlewareLinkTypeAndEnvFilterRequest struct {
	Type string `json:"type" binding:"required"`
	Env  int64  `json:"env" binding:"required"`
}

// 转换函数

// ToMiddlewareEnvPO 将DTO转换为PO
func (req *MiddlewareEnvCreateRequest) ToMiddlewareEnvPO() *po.MiddlewareEnv {
	return &po.MiddlewareEnv{
		Name:        req.Name,
		Description: req.Description,
	}
}

// ToMiddlewareEnvPO 将DTO转换为PO
func (req *MiddlewareEnvUpdateRequest) ToMiddlewareEnvPO() *po.MiddlewareEnv {
	return &po.MiddlewareEnv{
		ID:          req.ID,
		Name:        req.Name,
		Description: req.Description,
	}
}

// ToMiddlewareEnvResponse 将PO转换为DTO
func ToMiddlewareEnvResponse(env *po.MiddlewareEnv) *MiddlewareEnvResponse {
	return &MiddlewareEnvResponse{
		ID:          env.ID,
		Name:        env.Name,
		Description: env.Description,
		CreatedAt:   env.CreatedAt,
		UpdatedAt:   env.UpdatedAt,
	}
}

// ToMiddlewareLinkPO 将DTO转换为PO
func (req *MiddlewareLinkCreateRequest) ToMiddlewareLinkPO() *po.MiddlewareLink {
	return &po.MiddlewareLink{
		Env:         req.Env,
		Name:        req.Name,
		Type:        req.Type,
		URL:         req.URL,
		Description: req.Description,
	}
}

// ToMiddlewareLinkPO 将DTO转换为PO
func (req *MiddlewareLinkUpdateRequest) ToMiddlewareLinkPO() *po.MiddlewareLink {
	return &po.MiddlewareLink{
		ID:          req.ID,
		Env:         req.Env,
		Name:        req.Name,
		Type:        req.Type,
		URL:         req.URL,
		Description: req.Description,
	}
}

// ToMiddlewareLinkResponse 将PO转换为DTO
func ToMiddlewareLinkResponse(link *po.MiddlewareLink) *MiddlewareLinkResponse {
	response := &MiddlewareLinkResponse{
		ID:          link.ID,
		Env:         link.Env,
		Name:        link.Name,
		Type:        link.Type,
		URL:         link.URL,
		Description: link.Description,
		CreatedAt:   link.CreatedAt,
		UpdatedAt:   link.UpdatedAt,
	}

	if link.EnvInfo != nil {
		response.EnvInfo = ToMiddlewareEnvResponse(link.EnvInfo)
	}

	return response
}

// Topic配置相关DTO

// TopicConfigCreateRequest 创建Topic配置请求
type TopicConfigCreateRequest struct {
	Env         int64  `json:"env" binding:"required"`
	Name        string `json:"name" binding:"required"`
	Type        string `json:"type" binding:"required,oneof=kafka rocketmq"`
	Address     string `json:"address" binding:"required"`
	Username    string `json:"username"`
	Password    string `json:"password"`
	Description string `json:"description"`
}

// TopicConfigUpdateRequest 更新Topic配置请求
type TopicConfigUpdateRequest struct {
	ID          int64  `json:"id" binding:"required"`
	Env         int64  `json:"env" binding:"required"`
	Name        string `json:"name" binding:"required"`
	Type        string `json:"type" binding:"required,oneof=kafka rocketmq"`
	Address     string `json:"address" binding:"required"`
	Username    string `json:"username"`
	Password    string `json:"password"`
	Description string `json:"description"`
}

// TopicConfigResponse Topic配置响应
type TopicConfigResponse struct {
	ID          int64                  `json:"id"`
	Env         int64                  `json:"env"`
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`
	Address     string                 `json:"address"`
	Username    string                 `json:"username"`
	Password    string                 `json:"password,omitempty"` // 密码不返回
	Description string                 `json:"description"`
	TopicCount  *int                   `json:"topicCount,omitempty"`  // Topic数量
	GroupCount  *int                   `json:"groupCount,omitempty"`  // Group数量
	CreatedAt   time.Time              `json:"createdAt"`
	UpdatedAt   time.Time              `json:"updatedAt"`
	EnvInfo     *MiddlewareEnvResponse `json:"envInfo,omitempty"`
}

// TopicInfo Topic信息
type TopicInfo struct {
	Name        string `json:"name"`
	Partitions  int    `json:"partitions,omitempty"`
	Replicas    int    `json:"replicas,omitempty"`
	Description string `json:"description,omitempty"`
}

// GroupInfo Group信息
type GroupInfo struct {
	Name        string `json:"name"`
	State       string `json:"state,omitempty"`
	Members     int    `json:"members,omitempty"`
	Description string `json:"description,omitempty"`
}

// TopicListResponse Topic列表响应
type TopicListResponse struct {
	Topics []TopicInfo `json:"topics"`
	Groups []GroupInfo `json:"groups"`
}

// =============== 同步相关DTO ===============

// SyncTaskCreateRequest 创建同步任务请求
type SyncTaskCreateRequest struct {
	Name           string `json:"name" binding:"required"`           // 任务名称
	Type           string `json:"type" binding:"required"`           // 同步类型：kafka 或 rocketmq
	SourceConfigID int64  `json:"sourceConfigId" binding:"required"` // 源配置ID
	TargetConfigID int64  `json:"targetConfigId" binding:"required"` // 目标配置ID
	SyncTopics     bool   `json:"syncTopics"`                        // 是否同步Topic
	SyncGroups     bool   `json:"syncGroups"`                        // 是否同步Group
	SkipExisting   bool   `json:"skipExisting"`                      // 是否跳过已存在的项目
}

// ToSyncTaskPO 转换为PO对象
func (req *SyncTaskCreateRequest) ToSyncTaskPO() *po.SyncTask {
	return &po.SyncTask{
		Name:           req.Name,
		Type:           req.Type,
		SourceConfigID: req.SourceConfigID,
		TargetConfigID: req.TargetConfigID,
		Status:         "pending",
		Progress:       0,
		SyncTopics:     req.SyncTopics,
		SyncGroups:     req.SyncGroups,
		SkipExisting:   req.SkipExisting,
	}
}

// SyncTaskResponse 同步任务响应
type SyncTaskResponse struct {
	ID             int64                `json:"id"`
	Name           string               `json:"name"`
	Type           string               `json:"type"`
	SourceConfigID int64                `json:"sourceConfigId"`
	TargetConfigID int64                `json:"targetConfigId"`
	Status         string               `json:"status"`
	Progress       int                  `json:"progress"`
	TotalItems     int                  `json:"totalItems"`
	CompletedItems int                  `json:"completedItems"`
	SkippedItems   int                  `json:"skippedItems"`
	FailedItems    int                  `json:"failedItems"`
	SyncTopics     bool                 `json:"syncTopics"`
	SyncGroups     bool                 `json:"syncGroups"`
	SkipExisting   bool                 `json:"skipExisting"`
	ErrorMessage   string               `json:"errorMessage"`
	StartedAt      *time.Time           `json:"startedAt"`
	CompletedAt    *time.Time           `json:"completedAt"`
	CreatedAt      time.Time            `json:"createdAt"`
	UpdatedAt      time.Time            `json:"updatedAt"`
	SourceConfig   *TopicConfigResponse `json:"sourceConfig,omitempty"`
	TargetConfig   *TopicConfigResponse `json:"targetConfig,omitempty"`
}

// ToSyncTaskResponse 转换为响应对象
func ToSyncTaskResponse(task *po.SyncTask) *SyncTaskResponse {
	resp := &SyncTaskResponse{
		ID:             task.ID,
		Name:           task.Name,
		Type:           task.Type,
		SourceConfigID: task.SourceConfigID,
		TargetConfigID: task.TargetConfigID,
		Status:         task.Status,
		Progress:       task.Progress,
		TotalItems:     task.TotalItems,
		CompletedItems: task.CompletedItems,
		SkippedItems:   task.SkippedItems,
		FailedItems:    task.FailedItems,
		SyncTopics:     task.SyncTopics,
		SyncGroups:     task.SyncGroups,
		SkipExisting:   task.SkipExisting,
		ErrorMessage:   task.ErrorMessage,
		StartedAt:      task.StartedAt,
		CompletedAt:    task.CompletedAt,
		CreatedAt:      task.CreatedAt,
		UpdatedAt:      task.UpdatedAt,
	}

	if task.SourceConfig != nil {
		resp.SourceConfig = ToTopicConfigResponse(task.SourceConfig)
	}
	if task.TargetConfig != nil {
		resp.TargetConfig = ToTopicConfigResponse(task.TargetConfig)
	}

	return resp
}

// SyncItemResponse 同步项目响应
type SyncItemResponse struct {
	ID         int64     `json:"id"`
	TaskID     int64     `json:"taskId"`
	ItemType   string    `json:"itemType"`
	ItemName   string    `json:"itemName"`
	Status     string    `json:"status"`
	ErrorMsg   string    `json:"errorMsg"`
	SourceData string    `json:"sourceData"`
	CreatedAt  time.Time `json:"createdAt"`
	UpdatedAt  time.Time `json:"updatedAt"`
}

// ToSyncItemResponse 转换为响应对象
func ToSyncItemResponse(item *po.SyncItem) *SyncItemResponse {
	return &SyncItemResponse{
		ID:         item.ID,
		TaskID:     item.TaskID,
		ItemType:   item.ItemType,
		ItemName:   item.ItemName,
		Status:     item.Status,
		ErrorMsg:   item.ErrorMsg,
		SourceData: item.SourceData,
		CreatedAt:  item.CreatedAt,
		UpdatedAt:  item.UpdatedAt,
	}
}

// SyncLogResponse 同步日志响应
type SyncLogResponse struct {
	ID        int64     `json:"id"`
	TaskID    int64     `json:"taskId"`
	Level     string    `json:"level"`
	Message   string    `json:"message"`
	Details   string    `json:"details"`
	CreatedAt time.Time `json:"createdAt"`
}

// ToSyncLogResponse 转换为响应对象
func ToSyncLogResponse(log *po.SyncLog) *SyncLogResponse {
	return &SyncLogResponse{
		ID:        log.ID,
		TaskID:    log.TaskID,
		Level:     log.Level,
		Message:   log.Message,
		Details:   log.Details,
		CreatedAt: log.CreatedAt,
	}
}

// SyncPreviewRequest 同步预览请求
type SyncPreviewRequest struct {
	SourceConfigID int64 `json:"sourceConfigId" binding:"required"` // 源配置ID
	TargetConfigID int64 `json:"targetConfigId" binding:"required"` // 目标配置ID
	SyncTopics     bool  `json:"syncTopics"`                        // 是否同步Topic
	SyncGroups     bool  `json:"syncGroups"`                        // 是否同步Group
}

// SyncPreviewResponse 同步预览响应
type SyncPreviewResponse struct {
	SourceConfig *TopicConfigResponse `json:"sourceConfig"` // 源配置信息
	TargetConfig *TopicConfigResponse `json:"targetConfig"` // 目标配置信息
	TopicsToSync []TopicInfo          `json:"topicsToSync"` // 需要同步的Topic
	TopicsToSkip []TopicInfo          `json:"topicsToSkip"` // 需要跳过的Topic
	GroupsToSync []GroupInfo          `json:"groupsToSync"` // 需要同步的Group
	GroupsToSkip []GroupInfo          `json:"groupsToSkip"` // 需要跳过的Group
	TotalItems   int                  `json:"totalItems"`   // 总项目数
	ItemsToSync  int                  `json:"itemsToSync"`  // 需要同步的项目数
	ItemsToSkip  int                  `json:"itemsToSkip"`  // 需要跳过的项目数
	CanProceed   bool                 `json:"canProceed"`   // 是否可以继续
	ErrorMessage string               `json:"errorMessage"` // 错误信息
}

// 转换函数

// ToTopicConfigPO 将DTO转换为PO
func (req *TopicConfigCreateRequest) ToTopicConfigPO() *po.TopicConfig {
	return &po.TopicConfig{
		Env:         req.Env,
		Name:        req.Name,
		Type:        req.Type,
		Address:     req.Address,
		Username:    req.Username,
		Password:    req.Password,
		Description: req.Description,
	}
}

// ToTopicConfigPO 将DTO转换为PO
func (req *TopicConfigUpdateRequest) ToTopicConfigPO() *po.TopicConfig {
	return &po.TopicConfig{
		ID:          req.ID,
		Env:         req.Env,
		Name:        req.Name,
		Type:        req.Type,
		Address:     req.Address,
		Username:    req.Username,
		Password:    req.Password,
		Description: req.Description,
	}
}

// ToTopicConfigResponse 将PO转换为DTO
func ToTopicConfigResponse(config *po.TopicConfig) *TopicConfigResponse {
	response := &TopicConfigResponse{
		ID:       config.ID,
		Env:      config.Env,
		Name:     config.Name,
		Type:     config.Type,
		Address:  config.Address,
		Username: config.Username,
		// Password:    config.Password, // 不返回密码
		Description: config.Description,
		CreatedAt:   config.CreatedAt,
		UpdatedAt:   config.UpdatedAt,
	}

	if config.EnvInfo != nil {
		response.EnvInfo = ToMiddlewareEnvResponse(config.EnvInfo)
	}

	return response
}
