package po

import (
	"time"
)

// MiddlewareEnv 中间件环境表
type MiddlewareEnv struct {
	ID          int64     `gorm:"primaryKey;column:id" json:"id"`        // 自增ID
	Name        string    `gorm:"column:name" json:"name"`               // 环境标签，如阿里云、华为云等
	Description string    `gorm:"column:description" json:"description"` // 描述信息
	CreatedAt   time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt   time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 获取数据库表名
func (m *MiddlewareEnv) TableName() string {
	return "t_middleware_env"
}

// MiddlewareEnvColumns 获取数据库列名
var MiddlewareEnvColumns = struct {
	ID          string
	Name        string
	Description string
	CreatedAt   string
	UpdatedAt   string
}{
	ID:          "id",
	Name:        "name",
	Description: "description",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
}

// MiddlewareLink 中间件链接表
type MiddlewareLink struct {
	ID          int64          `gorm:"primaryKey;column:id" json:"id"`        // 自增ID
	Env         int64          `gorm:"column:env" json:"env"`                 // 所属环境ID
	Name        string         `gorm:"column:name" json:"name"`               // 链接名称
	Type        string         `gorm:"column:type" json:"type"`               // 具体属性类型
	URL         string         `gorm:"column:url" json:"url"`                 // 链接地址
	Description string         `gorm:"column:description" json:"description"` // 描述信息
	CreatedAt   time.Time      `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt   time.Time      `gorm:"column:updated_at" json:"updatedAt"`
	EnvInfo     *MiddlewareEnv `gorm:"foreignKey:Env;references:ID" json:"envInfo,omitempty"` // 环境信息，通过关联查询获取
}

// TableName 获取数据库表名
func (m *MiddlewareLink) TableName() string {
	return "t_middleware_link"
}

// MiddlewareLinkColumns 获取数据库列名
var MiddlewareLinkColumns = struct {
	ID          string
	Env         string
	Name        string
	Type        string
	URL         string
	Description string
	CreatedAt   string
	UpdatedAt   string
}{
	ID:          "id",
	Env:         "env",
	Name:        "name",
	Type:        "type",
	URL:         "url",
	Description: "description",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
}

// TopicConfig Topic配置表
type TopicConfig struct {
	ID          int64          `gorm:"primaryKey;column:id" json:"id"`        // 自增ID
	Env         int64          `gorm:"column:env" json:"env"`                 // 所属环境ID
	Name        string         `gorm:"column:name" json:"name"`               // 配置名称
	Type        string         `gorm:"column:type" json:"type"`               // 类型：kafka 或 rocketmq
	Address     string         `gorm:"column:address" json:"address"`         // 连接地址
	Username    string         `gorm:"column:username" json:"username"`       // 用户名（可选）
	Password    string         `gorm:"column:password" json:"password"`       // 密码（可选）
	Description string         `gorm:"column:description" json:"description"` // 描述信息
	CreatedAt   time.Time      `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt   time.Time      `gorm:"column:updated_at" json:"updatedAt"`
	EnvInfo     *MiddlewareEnv `gorm:"foreignKey:Env;references:ID" json:"envInfo,omitempty"` // 环境信息
}

// TableName 获取数据库表名
func (t *TopicConfig) TableName() string {
	return "t_topic_config"
}

// TopicConfigColumns 获取数据库列名
var TopicConfigColumns = struct {
	ID          string
	Env         string
	Name        string
	Type        string
	Address     string
	Username    string
	Password    string
	Description string
	CreatedAt   string
	UpdatedAt   string
}{
	ID:          "id",
	Env:         "env",
	Name:        "name",
	Type:        "type",
	Address:     "address",
	Username:    "username",
	Password:    "password",
	Description: "description",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
}

// SyncTask 同步任务表
type SyncTask struct {
	ID             int64        `gorm:"primaryKey;column:id" json:"id"`                // 自增ID
	Name           string       `gorm:"column:name" json:"name"`                       // 任务名称
	Type           string       `gorm:"column:type" json:"type"`                       // 同步类型：kafka 或 rocketmq
	SourceConfigID int64        `gorm:"column:source_config_id" json:"sourceConfigId"` // 源配置ID
	TargetConfigID int64        `gorm:"column:target_config_id" json:"targetConfigId"` // 目标配置ID
	Status         string       `gorm:"column:status" json:"status"`                   // 任务状态：pending, running, completed, failed, cancelled
	Progress       int          `gorm:"column:progress" json:"progress"`               // 进度百分比 0-100
	TotalItems     int          `gorm:"column:total_items" json:"totalItems"`          // 总项目数
	CompletedItems int          `gorm:"column:completed_items" json:"completedItems"`  // 已完成项目数
	SkippedItems   int          `gorm:"column:skipped_items" json:"skippedItems"`      // 跳过项目数
	FailedItems    int          `gorm:"column:failed_items" json:"failedItems"`        // 失败项目数
	SyncTopics     bool         `gorm:"column:sync_topics" json:"syncTopics"`          // 是否同步Topic
	SyncGroups     bool         `gorm:"column:sync_groups" json:"syncGroups"`          // 是否同步Group
	SkipExisting   bool         `gorm:"column:skip_existing" json:"skipExisting"`      // 是否跳过已存在的项目
	ErrorMessage   string       `gorm:"column:error_message" json:"errorMessage"`      // 错误信息
	StartedAt      *time.Time   `gorm:"column:started_at" json:"startedAt"`            // 开始时间
	CompletedAt    *time.Time   `gorm:"column:completed_at" json:"completedAt"`        // 完成时间
	CreatedAt      time.Time    `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt      time.Time    `gorm:"column:updated_at" json:"updatedAt"`
	SourceConfig   *TopicConfig `gorm:"foreignKey:SourceConfigID;references:ID" json:"sourceConfig,omitempty"` // 源配置信息
	TargetConfig   *TopicConfig `gorm:"foreignKey:TargetConfigID;references:ID" json:"targetConfig,omitempty"` // 目标配置信息
}

// TableName 获取数据库表名
func (s *SyncTask) TableName() string {
	return "t_sync_task"
}

// SyncTaskColumns 获取数据库列名
var SyncTaskColumns = struct {
	ID             string
	Name           string
	Type           string
	SourceConfigID string
	TargetConfigID string
	Status         string
	Progress       string
	TotalItems     string
	CompletedItems string
	SkippedItems   string
	FailedItems    string
	SyncTopics     string
	SyncGroups     string
	SkipExisting   string
	ErrorMessage   string
	StartedAt      string
	CompletedAt    string
	CreatedAt      string
	UpdatedAt      string
}{
	ID:             "id",
	Name:           "name",
	Type:           "type",
	SourceConfigID: "source_config_id",
	TargetConfigID: "target_config_id",
	Status:         "status",
	Progress:       "progress",
	TotalItems:     "total_items",
	CompletedItems: "completed_items",
	SkippedItems:   "skipped_items",
	FailedItems:    "failed_items",
	SyncTopics:     "sync_topics",
	SyncGroups:     "sync_groups",
	SkipExisting:   "skip_existing",
	ErrorMessage:   "error_message",
	StartedAt:      "started_at",
	CompletedAt:    "completed_at",
	CreatedAt:      "created_at",
	UpdatedAt:      "updated_at",
}

// SyncItem 同步项目表
type SyncItem struct {
	ID         int64     `gorm:"primaryKey;column:id" json:"id"`       // 自增ID
	TaskID     int64     `gorm:"column:task_id" json:"taskId"`         // 所属任务ID
	ItemType   string    `gorm:"column:item_type" json:"itemType"`     // 项目类型：topic 或 group
	ItemName   string    `gorm:"column:item_name" json:"itemName"`     // 项目名称
	Status     string    `gorm:"column:status" json:"status"`          // 状态：pending, completed, skipped, failed
	ErrorMsg   string    `gorm:"column:error_msg" json:"errorMsg"`     // 错误信息
	SourceData string    `gorm:"column:source_data" json:"sourceData"` // 源数据（JSON格式）
	CreatedAt  time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt  time.Time `gorm:"column:updated_at" json:"updatedAt"`
	Task       *SyncTask `gorm:"foreignKey:TaskID;references:ID" json:"task,omitempty"` // 任务信息
}

// TableName 获取数据库表名
func (s *SyncItem) TableName() string {
	return "t_sync_item"
}

// SyncItemColumns 获取数据库列名
var SyncItemColumns = struct {
	ID         string
	TaskID     string
	ItemType   string
	ItemName   string
	Status     string
	ErrorMsg   string
	SourceData string
	CreatedAt  string
	UpdatedAt  string
}{
	ID:         "id",
	TaskID:     "task_id",
	ItemType:   "item_type",
	ItemName:   "item_name",
	Status:     "status",
	ErrorMsg:   "error_msg",
	SourceData: "source_data",
	CreatedAt:  "created_at",
	UpdatedAt:  "updated_at",
}

// SyncLog 同步日志表
type SyncLog struct {
	ID        int64     `gorm:"primaryKey;column:id" json:"id"` // 自增ID
	TaskID    int64     `gorm:"column:task_id" json:"taskId"`   // 所属任务ID
	Level     string    `gorm:"column:level" json:"level"`      // 日志级别：info, warn, error
	Message   string    `gorm:"column:message" json:"message"`  // 日志消息
	Details   string    `gorm:"column:details" json:"details"`  // 详细信息（JSON格式）
	CreatedAt time.Time `gorm:"column:created_at" json:"createdAt"`
	Task      *SyncTask `gorm:"foreignKey:TaskID;references:ID" json:"task,omitempty"` // 任务信息
}

// TableName 获取数据库表名
func (s *SyncLog) TableName() string {
	return "t_sync_log"
}

// SyncLogColumns 获取数据库列名
var SyncLogColumns = struct {
	ID        string
	TaskID    string
	Level     string
	Message   string
	Details   string
	CreatedAt string
}{
	ID:        "id",
	TaskID:    "task_id",
	Level:     "level",
	Message:   "message",
	Details:   "details",
	CreatedAt: "created_at",
}

// TopicCache Topic缓存表
type TopicCache struct {
	ID          int64        `gorm:"primaryKey;column:id" json:"id"`         // 自增ID
	ConfigID    int64        `gorm:"column:config_id;index" json:"configId"` // Topic配置ID
	Name        string       `gorm:"column:name" json:"name"`                // Topic名称
	Partitions  int          `gorm:"column:partitions" json:"partitions"`    // 分区数
	Replicas    int          `gorm:"column:replicas" json:"replicas"`        // 副本数
	Description string       `gorm:"column:description" json:"description"`  // 描述信息
	CreatedAt   time.Time    `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt   time.Time    `gorm:"column:updated_at" json:"updatedAt"`
	TopicConfig *TopicConfig `gorm:"foreignKey:ConfigID;references:ID" json:"topicConfig,omitempty"` // 配置信息
}

// TableName 获取数据库表名
func (t *TopicCache) TableName() string {
	return "t_topic_cache"
}

// TopicCacheColumns 获取数据库列名
var TopicCacheColumns = struct {
	ID          string
	ConfigID    string
	Name        string
	Partitions  string
	Replicas    string
	Description string
	CreatedAt   string
	UpdatedAt   string
}{
	ID:          "id",
	ConfigID:    "config_id",
	Name:        "name",
	Partitions:  "partitions",
	Replicas:    "replicas",
	Description: "description",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
}

// GroupCache Group缓存表
type GroupCache struct {
	ID          int64        `gorm:"primaryKey;column:id" json:"id"`         // 自增ID
	ConfigID    int64        `gorm:"column:config_id;index" json:"configId"` // Topic配置ID
	Name        string       `gorm:"column:name" json:"name"`                // Group名称
	State       string       `gorm:"column:state" json:"state"`              // 状态
	Members     int          `gorm:"column:members" json:"members"`          // 成员数
	Description string       `gorm:"column:description" json:"description"`  // 描述信息
	CreatedAt   time.Time    `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt   time.Time    `gorm:"column:updated_at" json:"updatedAt"`
	TopicConfig *TopicConfig `gorm:"foreignKey:ConfigID;references:ID" json:"topicConfig,omitempty"` // 配置信息
}

// TableName 获取数据库表名
func (g *GroupCache) TableName() string {
	return "t_group_cache"
}

// GroupCacheColumns 获取数据库列名
var GroupCacheColumns = struct {
	ID          string
	ConfigID    string
	Name        string
	State       string
	Members     string
	Description string
	CreatedAt   string
	UpdatedAt   string
}{
	ID:          "id",
	ConfigID:    "config_id",
	Name:        "name",
	State:       "state",
	Members:     "members",
	Description: "description",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
}

// TopicGroupCacheMeta 缓存元数据表
type TopicGroupCacheMeta struct {
	ID           int64        `gorm:"primaryKey;column:id" json:"id"`               // 自增ID
	ConfigID     int64        `gorm:"column:config_id;uniqueIndex" json:"configId"` // Topic配置ID（唯一索引）
	LastSyncAt   time.Time    `gorm:"column:last_sync_at" json:"lastSyncAt"`        // 最后同步时间
	TopicCount   int          `gorm:"column:topic_count" json:"topicCount"`         // Topic数量
	GroupCount   int          `gorm:"column:group_count" json:"groupCount"`         // Group数量
	SyncStatus   string       `gorm:"column:sync_status" json:"syncStatus"`         // 同步状态：success, failed, syncing
	ErrorMessage string       `gorm:"column:error_message" json:"errorMessage"`     // 错误信息
	CreatedAt    time.Time    `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt    time.Time    `gorm:"column:updated_at" json:"updatedAt"`
	TopicConfig  *TopicConfig `gorm:"foreignKey:ConfigID;references:ID" json:"topicConfig,omitempty"` // 配置信息
}

// TableName 获取数据库表名
func (t *TopicGroupCacheMeta) TableName() string {
	return "t_topic_group_cache_meta"
}

// TopicGroupCacheMetaColumns 获取数据库列名
var TopicGroupCacheMetaColumns = struct {
	ID           string
	ConfigID     string
	LastSyncAt   string
	TopicCount   string
	GroupCount   string
	SyncStatus   string
	ErrorMessage string
	CreatedAt    string
	UpdatedAt    string
}{
	ID:           "id",
	ConfigID:     "config_id",
	LastSyncAt:   "last_sync_at",
	TopicCount:   "topic_count",
	GroupCount:   "group_count",
	SyncStatus:   "sync_status",
	ErrorMessage: "error_message",
	CreatedAt:    "created_at",
	UpdatedAt:    "updated_at",
}
