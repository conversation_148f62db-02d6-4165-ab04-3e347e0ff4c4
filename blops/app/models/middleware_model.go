package models

import (
	"blops/app/models/po"
	. "blops/sql"
	"context"
)

// MiddlewareEnvMgr 环境管理器
type MiddlewareEnvMgr struct {
	*BaseMgr
}

// NewMiddlewareEnvMgr 创建环境管理器
func NewMiddlewareEnvMgr() *MiddlewareEnvMgr {
	ctx, cancel := context.WithCancel(context.Background())
	return &MiddlewareEnvMgr{BaseMgr: &BaseMgr{DB: GDB.Table("t_middleware_env"), IsRelated: GlobalIsRelated, Ctx: ctx, Cancel: cancel, Timeout: -1}}
}

// GetTableName 获取表名
func (obj *MiddlewareEnvMgr) GetTableName() string {
	return "t_middleware_env"
}

// Reset 重置gorm会话
func (obj *MiddlewareEnvMgr) Reset() *MiddlewareEnvMgr {
	obj.New()
	return obj
}

// Get 获取单个环境
func (obj *MiddlewareEnvMgr) Get(id int64) (result po.MiddlewareEnv, err error) {
	err = obj.DB.WithContext(obj.Ctx).Where("id = ?", id).First(&result).Error
	return
}

// Gets 获取所有环境
func (obj *MiddlewareEnvMgr) Gets() (results []*po.MiddlewareEnv, err error) {
	err = obj.DB.WithContext(obj.Ctx).Find(&results).Error
	return
}

// Create 创建环境
func (obj *MiddlewareEnvMgr) Create(env *po.MiddlewareEnv) error {
	return obj.DB.WithContext(obj.Ctx).Create(env).Error
}

// Update 更新环境
func (obj *MiddlewareEnvMgr) Update(env *po.MiddlewareEnv) error {
	return obj.DB.WithContext(obj.Ctx).Where("id = ?", env.ID).Updates(env).Error
}

// Delete 删除环境
func (obj *MiddlewareEnvMgr) Delete(id int64) error {
	return obj.DB.WithContext(obj.Ctx).Where("id = ?", id).Delete(&po.MiddlewareEnv{}).Error
}

// MiddlewareLinkMgr 链接管理器
type MiddlewareLinkMgr struct {
	*BaseMgr
}

// NewMiddlewareLinkMgr 创建链接管理器
func NewMiddlewareLinkMgr() *MiddlewareLinkMgr {
	ctx, cancel := context.WithCancel(context.Background())
	return &MiddlewareLinkMgr{BaseMgr: &BaseMgr{DB: GDB.Table("t_middleware_link"), IsRelated: GlobalIsRelated, Ctx: ctx, Cancel: cancel, Timeout: -1}}
}

// GetTableName 获取表名
func (obj *MiddlewareLinkMgr) GetTableName() string {
	return "t_middleware_link"
}

// Reset 重置gorm会话
func (obj *MiddlewareLinkMgr) Reset() *MiddlewareLinkMgr {
	obj.New()
	return obj
}

// Get 获取单个链接
func (obj *MiddlewareLinkMgr) Get(id int64) (result po.MiddlewareLink, err error) {
	err = obj.DB.WithContext(obj.Ctx).Where("id = ?", id).First(&result).Error
	return
}

// Gets 获取所有链接
func (obj *MiddlewareLinkMgr) Gets() (results []*po.MiddlewareLink, err error) {
	err = obj.DB.WithContext(obj.Ctx).Find(&results).Error
	return
}

// GetsByType 根据类型获取链接
func (obj *MiddlewareLinkMgr) GetsByType(linkType string) (results []*po.MiddlewareLink, err error) {
	err = obj.DB.WithContext(obj.Ctx).Where("type = ?", linkType).Find(&results).Error
	return
}

// GetsByEnv 根据环境ID获取链接
func (obj *MiddlewareLinkMgr) GetsByEnv(envID int64) (results []*po.MiddlewareLink, err error) {
	err = obj.DB.WithContext(obj.Ctx).Where("env = ?", envID).Find(&results).Error
	return
}

// GetsByTypeAndEnv 根据类型和环境ID获取链接
func (obj *MiddlewareLinkMgr) GetsByTypeAndEnv(linkType string, envID int64) (results []*po.MiddlewareLink, err error) {
	err = obj.DB.WithContext(obj.Ctx).Where("type = ? AND env = ?", linkType, envID).Find(&results).Error
	return
}

// GetsWithEnvInfo 获取所有链接并包含环境信息
func (obj *MiddlewareLinkMgr) GetsWithEnvInfo() (results []*po.MiddlewareLink, err error) {
	err = obj.DB.WithContext(obj.Ctx).Preload("EnvInfo").Find(&results).Error
	return
}

// GetsByTypeWithEnvInfo 根据类型获取链接并包含环境信息
func (obj *MiddlewareLinkMgr) GetsByTypeWithEnvInfo(linkType string) (results []*po.MiddlewareLink, err error) {
	err = obj.DB.WithContext(obj.Ctx).Where("type = ?", linkType).Preload("EnvInfo").Find(&results).Error
	return
}

// Create 创建链接
func (obj *MiddlewareLinkMgr) Create(link *po.MiddlewareLink) error {
	return obj.DB.WithContext(obj.Ctx).Create(link).Error
}

// Update 更新链接
func (obj *MiddlewareLinkMgr) Update(link *po.MiddlewareLink) error {
	return obj.DB.WithContext(obj.Ctx).Where("id = ?", link.ID).Updates(link).Error
}

// Delete 删除链接
func (obj *MiddlewareLinkMgr) Delete(id int64) error {
	return obj.DB.WithContext(obj.Ctx).Where("id = ?", id).Delete(&po.MiddlewareLink{}).Error
}

// TopicConfigMgr Topic配置管理器
type TopicConfigMgr struct {
	*BaseMgr
}

// NewTopicConfigMgr 创建Topic配置管理器
func NewTopicConfigMgr() *TopicConfigMgr {
	ctx, cancel := context.WithCancel(context.Background())
	return &TopicConfigMgr{BaseMgr: &BaseMgr{DB: GDB.Table("t_topic_config"), IsRelated: GlobalIsRelated, Ctx: ctx, Cancel: cancel, Timeout: -1}}
}

// GetTableName 获取表名
func (obj *TopicConfigMgr) GetTableName() string {
	return "t_topic_config"
}

// Reset 重置gorm会话
func (obj *TopicConfigMgr) Reset() *TopicConfigMgr {
	obj.New()
	return obj
}

// Get 获取单个Topic配置
func (obj *TopicConfigMgr) Get(id int64) (result po.TopicConfig, err error) {
	err = obj.DB.WithContext(obj.Ctx).Where("id = ?", id).First(&result).Error
	return
}

// Gets 获取所有Topic配置
func (obj *TopicConfigMgr) Gets() (results []*po.TopicConfig, err error) {
	err = obj.DB.WithContext(obj.Ctx).Find(&results).Error
	return
}

// GetsByType 根据类型获取Topic配置
func (obj *TopicConfigMgr) GetsByType(configType string) (results []*po.TopicConfig, err error) {
	err = obj.DB.WithContext(obj.Ctx).Where("type = ?", configType).Find(&results).Error
	return
}

// GetsByEnv 根据环境ID获取Topic配置
func (obj *TopicConfigMgr) GetsByEnv(envID int64) (results []*po.TopicConfig, err error) {
	err = obj.DB.WithContext(obj.Ctx).Where("env = ?", envID).Find(&results).Error
	return
}

// GetsByTypeAndEnv 根据类型和环境ID获取Topic配置
func (obj *TopicConfigMgr) GetsByTypeAndEnv(configType string, envID int64) (results []*po.TopicConfig, err error) {
	err = obj.DB.WithContext(obj.Ctx).Where("type = ? AND env = ?", configType, envID).Find(&results).Error
	return
}

// GetsWithEnvInfo 获取所有Topic配置并包含环境信息
func (obj *TopicConfigMgr) GetsWithEnvInfo() (results []*po.TopicConfig, err error) {
	err = obj.DB.WithContext(obj.Ctx).Preload("EnvInfo").Find(&results).Error
	return
}

// GetsByTypeWithEnvInfo 根据类型获取Topic配置并包含环境信息
func (obj *TopicConfigMgr) GetsByTypeWithEnvInfo(configType string) (results []*po.TopicConfig, err error) {
	err = obj.DB.WithContext(obj.Ctx).Where("type = ?", configType).Preload("EnvInfo").Find(&results).Error
	return
}

// TopicCacheMgr Topic缓存管理器
type TopicCacheMgr struct {
	*BaseMgr
}

// NewTopicCacheMgr 创建Topic缓存管理器
func NewTopicCacheMgr() *TopicCacheMgr {
	ctx, cancel := context.WithCancel(context.Background())
	return &TopicCacheMgr{BaseMgr: &BaseMgr{DB: GDB.Table("t_topic_cache"), IsRelated: GlobalIsRelated, Ctx: ctx, Cancel: cancel, Timeout: -1}}
}

// GetTableName 获取表名
func (obj *TopicCacheMgr) GetTableName() string {
	return "t_topic_cache"
}

// Reset 重置gorm会话
func (obj *TopicCacheMgr) Reset() *TopicCacheMgr {
	obj.New()
	return obj
}

// Get 根据主键获取一条记录
func (obj *TopicCacheMgr) Get(id int64) (result *po.TopicCache, err error) {
	err = obj.DB.WithContext(obj.Ctx).Where("id = ?", id).First(&result).Error
	return
}

// Gets 获取所有记录
func (obj *TopicCacheMgr) Gets() (results []*po.TopicCache, err error) {
	err = obj.DB.WithContext(obj.Ctx).Find(&results).Error
	return
}

// GetsByConfigID 根据配置ID获取Topic缓存
func (obj *TopicCacheMgr) GetsByConfigID(configID int64) (results []*po.TopicCache, err error) {
	err = obj.DB.WithContext(obj.Ctx).Where("config_id = ?", configID).Find(&results).Error
	return
}

// Create 创建记录
func (obj *TopicCacheMgr) Create(topic *po.TopicCache) error {
	return obj.DB.WithContext(obj.Ctx).Create(topic).Error
}

// Update 更新记录
func (obj *TopicCacheMgr) Update(topic *po.TopicCache) error {
	return obj.DB.WithContext(obj.Ctx).Save(topic).Error
}

// Delete 删除记录
func (obj *TopicCacheMgr) Delete(id int64) error {
	return obj.DB.WithContext(obj.Ctx).Where("id = ?", id).Delete(&po.TopicCache{}).Error
}

// DeleteByConfigID 根据配置ID删除Topic缓存
func (obj *TopicCacheMgr) DeleteByConfigID(configID int64) error {
	return obj.DB.WithContext(obj.Ctx).Where("config_id = ?", configID).Delete(&po.TopicCache{}).Error
}

// BatchCreate 批量创建Topic缓存
func (obj *TopicCacheMgr) BatchCreate(topics []*po.TopicCache) error {
	if len(topics) == 0 {
		return nil
	}
	return obj.DB.WithContext(obj.Ctx).Create(&topics).Error
}

// GroupCacheMgr Group缓存管理器
type GroupCacheMgr struct {
	*BaseMgr
}

// NewGroupCacheMgr 创建Group缓存管理器
func NewGroupCacheMgr() *GroupCacheMgr {
	ctx, cancel := context.WithCancel(context.Background())
	return &GroupCacheMgr{BaseMgr: &BaseMgr{DB: GDB.Table("t_group_cache"), IsRelated: GlobalIsRelated, Ctx: ctx, Cancel: cancel, Timeout: -1}}
}

// GetTableName 获取表名
func (obj *GroupCacheMgr) GetTableName() string {
	return "t_group_cache"
}

// Reset 重置gorm会话
func (obj *GroupCacheMgr) Reset() *GroupCacheMgr {
	obj.New()
	return obj
}

// Get 根据主键获取一条记录
func (obj *GroupCacheMgr) Get(id int64) (result *po.GroupCache, err error) {
	err = obj.DB.WithContext(obj.Ctx).Where("id = ?", id).First(&result).Error
	return
}

// Gets 获取所有记录
func (obj *GroupCacheMgr) Gets() (results []*po.GroupCache, err error) {
	err = obj.DB.WithContext(obj.Ctx).Find(&results).Error
	return
}

// GetsByConfigID 根据配置ID获取Group缓存
func (obj *GroupCacheMgr) GetsByConfigID(configID int64) (results []*po.GroupCache, err error) {
	err = obj.DB.WithContext(obj.Ctx).Where("config_id = ?", configID).Find(&results).Error
	return
}

// Create 创建记录
func (obj *GroupCacheMgr) Create(group *po.GroupCache) error {
	return obj.DB.WithContext(obj.Ctx).Create(group).Error
}

// Update 更新记录
func (obj *GroupCacheMgr) Update(group *po.GroupCache) error {
	return obj.DB.WithContext(obj.Ctx).Save(group).Error
}

// Delete 删除记录
func (obj *GroupCacheMgr) Delete(id int64) error {
	return obj.DB.WithContext(obj.Ctx).Where("id = ?", id).Delete(&po.GroupCache{}).Error
}

// DeleteByConfigID 根据配置ID删除Group缓存
func (obj *GroupCacheMgr) DeleteByConfigID(configID int64) error {
	return obj.DB.WithContext(obj.Ctx).Where("config_id = ?", configID).Delete(&po.GroupCache{}).Error
}

// BatchCreate 批量创建Group缓存
func (obj *GroupCacheMgr) BatchCreate(groups []*po.GroupCache) error {
	if len(groups) == 0 {
		return nil
	}
	return obj.DB.WithContext(obj.Ctx).Create(&groups).Error
}

// TopicGroupCacheMetaMgr 缓存元数据管理器
type TopicGroupCacheMetaMgr struct {
	*BaseMgr
}

// NewTopicGroupCacheMetaMgr 创建缓存元数据管理器
func NewTopicGroupCacheMetaMgr() *TopicGroupCacheMetaMgr {
	ctx, cancel := context.WithCancel(context.Background())
	return &TopicGroupCacheMetaMgr{BaseMgr: &BaseMgr{DB: GDB.Table("t_topic_group_cache_meta"), IsRelated: GlobalIsRelated, Ctx: ctx, Cancel: cancel, Timeout: -1}}
}

// GetTableName 获取表名
func (obj *TopicGroupCacheMetaMgr) GetTableName() string {
	return "t_topic_group_cache_meta"
}

// Reset 重置gorm会话
func (obj *TopicGroupCacheMetaMgr) Reset() *TopicGroupCacheMetaMgr {
	obj.New()
	return obj
}

// Get 根据主键获取一条记录
func (obj *TopicGroupCacheMetaMgr) Get(id int64) (result *po.TopicGroupCacheMeta, err error) {
	err = obj.DB.WithContext(obj.Ctx).Where("id = ?", id).First(&result).Error
	return
}

// GetByConfigID 根据配置ID获取缓存元数据
func (obj *TopicGroupCacheMetaMgr) GetByConfigID(configID int64) (result *po.TopicGroupCacheMeta, err error) {
	err = obj.DB.WithContext(obj.Ctx).Where("config_id = ?", configID).First(&result).Error
	return
}

// Create 创建记录
func (obj *TopicGroupCacheMetaMgr) Create(meta *po.TopicGroupCacheMeta) error {
	return obj.DB.WithContext(obj.Ctx).Create(meta).Error
}

// Update 更新记录
func (obj *TopicGroupCacheMetaMgr) Update(meta *po.TopicGroupCacheMeta) error {
	return obj.DB.WithContext(obj.Ctx).Save(meta).Error
}

// Delete 删除记录
func (obj *TopicGroupCacheMetaMgr) Delete(id int64) error {
	return obj.DB.WithContext(obj.Ctx).Where("id = ?", id).Delete(&po.TopicGroupCacheMeta{}).Error
}

// DeleteByConfigID 根据配置ID删除缓存元数据
func (obj *TopicGroupCacheMetaMgr) DeleteByConfigID(configID int64) error {
	return obj.DB.WithContext(obj.Ctx).Where("config_id = ?", configID).Delete(&po.TopicGroupCacheMeta{}).Error
}

// CreateOrUpdate 创建或更新缓存元数据
func (obj *TopicGroupCacheMetaMgr) CreateOrUpdate(meta *po.TopicGroupCacheMeta) error {
	var existing po.TopicGroupCacheMeta

	// 使用 SELECT FOR UPDATE 避免并发问题，并优化查询
	err := obj.DB.WithContext(obj.Ctx).
		Select("id", "created_at").
		Where("config_id = ?", meta.ConfigID).
		First(&existing).Error

	if err != nil {
		if err.Error() == "record not found" {
			// 记录不存在，创建新记录
			return obj.Create(meta)
		}
		return err
	}

	// 记录存在，更新现有记录
	meta.ID = existing.ID
	meta.CreatedAt = existing.CreatedAt

	// 只更新需要更新的字段，避免全表更新
	return obj.DB.WithContext(obj.Ctx).
		Model(meta).
		Select("sync_status", "error_message", "last_sync_at", "topic_count", "group_count", "updated_at").
		Where("id = ?", meta.ID).
		Updates(meta).Error
}

// Create 创建Topic配置
func (obj *TopicConfigMgr) Create(config *po.TopicConfig) error {
	return obj.DB.WithContext(obj.Ctx).Create(config).Error
}

// Update 更新Topic配置
func (obj *TopicConfigMgr) Update(config *po.TopicConfig) error {
	return obj.DB.WithContext(obj.Ctx).Where("id = ?", config.ID).Updates(config).Error
}

// Delete 删除Topic配置
func (obj *TopicConfigMgr) Delete(id int64) error {
	return obj.DB.WithContext(obj.Ctx).Where("id = ?", id).Delete(&po.TopicConfig{}).Error
}
