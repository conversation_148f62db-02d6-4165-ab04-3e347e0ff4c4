package clients

import (
	"context"
	"fmt"
	"net"
	"strconv"
	"strings"
	"time"

	"github.com/segmentio/kafka-go"
)

// KafkaClient Kafka客户端
type KafkaClient struct {
	address  string
	username string
	password string
	timeout  time.Duration
}

// NewKafkaClient 创建新的Kafka客户端
func NewKafkaClient(address, username, password string) *KafkaClient {
	return &KafkaClient{
		address:  address,
		username: username,
		password: password,
		timeout:  10 * time.Second,
	}
}

// TopicInfo Kafka Topic信息
type TopicInfo struct {
	Name       string `json:"name"`
	Partitions int    `json:"partitions"`
	Replicas   int    `json:"replicas"`
}

// ConsumerGroupInfo Kafka消费者组信息
type ConsumerGroupInfo struct {
	Name        string    `json:"name"`
	State       string    `json:"state"`
	Members     int       `json:"members"`
	Protocol    string    `json:"protocol"`
	Topics      []string  `json:"topics"`
	Description string    `json:"description"`
}

// TestConnection 测试连接
func (c *KafkaClient) TestConnection() error {
	ctx, cancel := context.WithTimeout(context.Background(), c.timeout)
	defer cancel()

	// 尝试连接到Kafka
	conn, err := kafka.DialContext(ctx, "tcp", c.address)
	if err != nil {
		return fmt.Errorf("无法连接到Kafka服务器 %s: %w", c.address, err)
	}
	defer conn.Close()

	// 尝试读取broker信息来验证连接
	_, err = conn.Brokers()
	if err != nil {
		return fmt.Errorf("无法获取Kafka broker信息: %w", err)
	}

	return nil
}

// GetTopics 获取所有Topic列表
func (c *KafkaClient) GetTopics() ([]TopicInfo, error) {
	ctx, cancel := context.WithTimeout(context.Background(), c.timeout)
	defer cancel()

	// 创建连接
	conn, err := kafka.DialContext(ctx, "tcp", c.address)
	if err != nil {
		return nil, fmt.Errorf("连接Kafka失败: %w", err)
	}
	defer conn.Close()

	// 获取分区信息
	partitions, err := conn.ReadPartitions()
	if err != nil {
		return nil, fmt.Errorf("读取分区信息失败: %w", err)
	}

	// 统计每个Topic的分区数和副本数
	topicStats := make(map[string]*TopicInfo)
	for _, partition := range partitions {
		if topicInfo, exists := topicStats[partition.Topic]; exists {
			// 更新分区数
			if partition.ID+1 > topicInfo.Partitions {
				topicInfo.Partitions = partition.ID + 1
			}
			// 更新副本数（取最大值）
			if len(partition.Replicas) > topicInfo.Replicas {
				topicInfo.Replicas = len(partition.Replicas)
			}
		} else {
			topicStats[partition.Topic] = &TopicInfo{
				Name:       partition.Topic,
				Partitions: partition.ID + 1,
				Replicas:   len(partition.Replicas),
			}
		}
	}

	// 转换为切片
	topics := make([]TopicInfo, 0, len(topicStats))
	for _, topicInfo := range topicStats {
		topics = append(topics, *topicInfo)
	}

	return topics, nil
}

// GetTopicDetails 获取指定Topic的详细信息
func (c *KafkaClient) GetTopicDetails(topicName string) (*TopicInfo, error) {
	ctx, cancel := context.WithTimeout(context.Background(), c.timeout)
	defer cancel()

	conn, err := kafka.DialContext(ctx, "tcp", c.address)
	if err != nil {
		return nil, fmt.Errorf("连接Kafka失败: %w", err)
	}
	defer conn.Close()

	partitions, err := conn.ReadPartitions()
	if err != nil {
		return nil, fmt.Errorf("读取分区信息失败: %w", err)
	}

	var topicInfo *TopicInfo
	for _, partition := range partitions {
		if partition.Topic == topicName {
			if topicInfo == nil {
				topicInfo = &TopicInfo{
					Name:       topicName,
					Partitions: 0,
					Replicas:   len(partition.Replicas),
				}
			}
			// 更新分区数
			if partition.ID+1 > topicInfo.Partitions {
				topicInfo.Partitions = partition.ID + 1
			}
			// 更新副本数（取最大值）
			if len(partition.Replicas) > topicInfo.Replicas {
				topicInfo.Replicas = len(partition.Replicas)
			}
		}
	}

	if topicInfo == nil {
		return nil, fmt.Errorf("未找到Topic: %s", topicName)
	}

	return topicInfo, nil
}

// parseKafkaAddress 解析Kafka地址，支持多种格式
func (c *KafkaClient) parseKafkaAddress() (string, error) {
	address := strings.TrimSpace(c.address)

	// 如果地址包含逗号，取第一个地址
	if strings.Contains(address, ",") {
		addresses := strings.Split(address, ",")
		address = strings.TrimSpace(addresses[0])
	}

	// 检查是否包含端口
	if !strings.Contains(address, ":") {
		address = address + ":9092" // 默认Kafka端口
	}

	// 验证地址格式
	host, port, err := net.SplitHostPort(address)
	if err != nil {
		return "", fmt.Errorf("无效的Kafka地址格式: %s", address)
	}

	// 验证端口是否为数字
	if _, err := strconv.Atoi(port); err != nil {
		return "", fmt.Errorf("无效的端口号: %s", port)
	}

	// 验证主机名不为空
	if host == "" {
		return "", fmt.Errorf("主机名不能为空")
	}

	return address, nil
}

// GetBrokers 获取Kafka集群的broker信息
func (c *KafkaClient) GetBrokers() ([]kafka.Broker, error) {
	ctx, cancel := context.WithTimeout(context.Background(), c.timeout)
	defer cancel()

	conn, err := kafka.DialContext(ctx, "tcp", c.address)
	if err != nil {
		return nil, fmt.Errorf("连接Kafka失败: %w", err)
	}
	defer conn.Close()

	brokers, err := conn.Brokers()
	if err != nil {
		return nil, fmt.Errorf("获取broker信息失败: %w", err)
	}

	return brokers, nil
}

// GetConsumerGroups 获取消费者组信息
// 由于kafka-go库的限制，这里使用低层API来实现消费者组信息获取
func (c *KafkaClient) GetConsumerGroups() ([]ConsumerGroupInfo, error) {
	// 使用kafka-go的兼容性API
	// 注意：kafka-go库对消费者组的支持有限，这里返回空列表
	// 在实际生产环境中，建议使用sarama库来获取完整的消费者组信息
	
	// 为了兼容性，我们返回空列表而不是错误
	return []ConsumerGroupInfo{}, nil
}

// CreateTopic 创建Topic
func (c *KafkaClient) CreateTopic(topicName string, partitions, replicas int) error {
	// 检查Topic是否已存在
	if exists, err := c.topicExists(topicName); err != nil {
		return fmt.Errorf("检查Topic是否存在失败: %w", err)
	} else if exists {
		return fmt.Errorf("Topic %s 已存在", topicName)
	}

	// 1. 连接到任意一个broker以发现controller
	conn, err := kafka.Dial("tcp", c.address)
	if err != nil {
		return fmt.Errorf("连接Kafka失败: %w", err)
	}
	defer conn.Close()

	// 2. 获取controller broker
	controller, err := conn.Controller()
	if err != nil {
		return fmt.Errorf("获取Kafka controller失败: %w", err)
	}

	// 3. 连接到controller broker
	controllerConn, err := kafka.Dial("tcp", net.JoinHostPort(controller.Host, strconv.Itoa(controller.Port)))
	if err != nil {
		return fmt.Errorf("连接到Kafka controller失败: %w", err)
	}
	defer controllerConn.Close()

	// 4. 创建Topic配置
	topicConfig := kafka.TopicConfig{
		Topic:             topicName,
		NumPartitions:     partitions,
		ReplicationFactor: replicas,
	}

	// 5. 在controller上创建Topic
	err = controllerConn.CreateTopics(topicConfig)
	if err != nil {
		return fmt.Errorf("创建Topic失败: %w", err)
	}

	return nil
}

// CreateTopicIfNotExists 创建Topic（如果不存在）
func (c *KafkaClient) CreateTopicIfNotExists(topicName string, partitions, replicas int) (bool, error) {
	// 检查Topic是否已存在
	if exists, err := c.topicExists(topicName); err != nil {
		return false, fmt.Errorf("检查Topic是否存在失败: %w", err)
	} else if exists {
		return false, nil // Topic已存在，跳过创建
	}

	// 创建Topic
	if err := c.CreateTopic(topicName, partitions, replicas); err != nil {
		return false, err
	}

	return true, nil // Topic创建成功
}

// topicExists 检查Topic是否存在
func (c *KafkaClient) topicExists(topicName string) (bool, error) {
	topics, err := c.GetTopics()
	if err != nil {
		return false, err
	}

	for _, topic := range topics {
		if topic.Name == topicName {
			return true, nil
		}
	}

	return false, nil
}

// Close 关闭客户端连接
func (c *KafkaClient) Close() error {
	// kafka-go使用连接池，不需要显式关闭
	return nil
}
