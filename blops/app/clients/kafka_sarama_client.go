package clients

import (
	"fmt"
	"strings"
	"time"

	"github.com/IBM/sarama"
)

// KafkaSaramaClient 使用sarama库的Kafka客户端
// 支持完整的消费者组查询功能

type KafkaSaramaClient struct {
	address  string
	username string
	password string
	timeout  time.Duration
}

// NewKafkaSaramaClient 创建新的Kafka客户端
func NewKafkaSaramaClient(address, username, password string) *KafkaSaramaClient {
	return &KafkaSaramaClient{
		address:  address,
		username: username,
		password: password,
		timeout:  30 * time.Second, // 增加超时时间到30秒
	}
}

// NewKafkaSaramaClientWithTimeout 创建带自定义超时的Kafka客户端
func NewKafkaSaramaClientWithTimeout(address, username, password string, timeout time.Duration) *KafkaSaramaClient {
	return &KafkaSaramaClient{
		address:  address,
		username: username,
		password: password,
		timeout:  timeout,
	}
}

// KafkaSaramaTopicInfo Kafka Topic信息
type KafkaSaramaTopicInfo struct {
	Name       string `json:"name"`
	Partitions int    `json:"partitions"`
	Replicas   int    `json:"replicas"`
}

// KafkaConsumerGroupInfo Kafka消费者组信息
type KafkaConsumerGroupInfo struct {
	Name        string   `json:"name"`
	State       string   `json:"state"`
	Members     int      `json:"members"`
	Topics      []string `json:"topics"`
	Description string   `json:"description"`
}

// getSaramaConfig 获取Sarama配置
func (c *KafkaSaramaClient) getSaramaConfig() *sarama.Config {
	config := sarama.NewConfig()
	config.Net.DialTimeout = c.timeout
	config.Net.ReadTimeout = c.timeout
	config.Net.WriteTimeout = c.timeout

	// 设置更宽松的连接参数
	config.Net.KeepAlive = 30 * time.Second
	config.Metadata.Retry.Max = 3
	config.Metadata.Retry.Backoff = 250 * time.Millisecond
	config.Metadata.RefreshFrequency = 10 * time.Minute
	config.Metadata.Full = true
	config.Metadata.AllowAutoTopicCreation = false

	// 设置版本兼容性
	config.Version = sarama.V2_6_0_0

	// 设置客户端ID
	config.ClientID = "blops-kafka-client"

	// 设置消费者配置
	config.Consumer.Return.Errors = true
	config.Consumer.Offsets.Initial = sarama.OffsetNewest

	// 设置生产者配置
	config.Producer.Return.Successes = true
	config.Producer.Return.Errors = true
	config.Producer.RequiredAcks = sarama.WaitForAll
	config.Producer.Retry.Max = 3

	// 错误处理配置
	config.Consumer.Return.Errors = true
	config.Producer.Return.Successes = true
	config.Producer.Return.Errors = true

	if c.username != "" && c.password != "" {
		config.Net.SASL.Enable = true
		config.Net.SASL.Mechanism = sarama.SASLTypePlaintext
		config.Net.SASL.User = c.username
		config.Net.SASL.Password = c.password
	}

	return config
}

// TestConnection 测试连接
func (c *KafkaSaramaClient) TestConnection() error {
	config := c.getSaramaConfig()

	// 添加重试机制
	var lastErr error
	for i := 0; i < 3; i++ {
		admin, err := sarama.NewClusterAdmin(strings.Split(c.address, ","), config)
		if err != nil {
			lastErr = fmt.Errorf("无法连接到Kafka集群: %w", err)
			if i < 2 { // 不是最后一次重试
				time.Sleep(time.Duration(i+1) * time.Second)
				continue
			}
			return lastErr
		}

		// 尝试获取Topic列表验证连接
		_, err = admin.ListTopics()
		admin.Close()

		if err != nil {
			lastErr = fmt.Errorf("无法获取Topic列表: %w", err)
			if i < 2 { // 不是最后一次重试
				time.Sleep(time.Duration(i+1) * time.Second)
				continue
			}
			return lastErr
		}

		return nil // 成功
	}

	return lastErr
}

// GetTopics 获取所有Topic列表
func (c *KafkaSaramaClient) GetTopics() ([]KafkaSaramaTopicInfo, error) {
	config := c.getSaramaConfig()

	// 添加重试机制
	var lastErr error
	for i := 0; i < 3; i++ {
		admin, err := sarama.NewClusterAdmin(strings.Split(c.address, ","), config)
		if err != nil {
			lastErr = fmt.Errorf("创建Kafka管理员客户端失败: %w", err)
			if i < 2 {
				time.Sleep(time.Duration(i+1) * time.Second)
				continue
			}
			return nil, lastErr
		}

		topics, err := admin.ListTopics()
		if err != nil {
			admin.Close()
			lastErr = fmt.Errorf("获取Topic列表失败: %w", err)
			if i < 2 {
				time.Sleep(time.Duration(i+1) * time.Second)
				continue
			}
			return nil, lastErr
		}

		var topicList []KafkaSaramaTopicInfo
		for topicName, detail := range topics {
			// 过滤掉内部Topic
			if strings.HasPrefix(topicName, "__") {
				continue
			}

			// 计算副本数
			replicas := 0
			if len(detail.ReplicaAssignment) > 0 {
				for _, replicasList := range detail.ReplicaAssignment {
					if len(replicasList) > replicas {
						replicas = len(replicasList)
					}
				}
			}

			topicList = append(topicList, KafkaSaramaTopicInfo{
				Name:       topicName,
				Partitions: int(detail.NumPartitions),
				Replicas:   replicas,
			})
		}

		admin.Close()
		return topicList, nil
	}

	return nil, lastErr
}

// GetConsumerGroups 获取消费者组信息
func (c *KafkaSaramaClient) GetConsumerGroups() ([]KafkaConsumerGroupInfo, error) {
	config := c.getSaramaConfig()

	// 添加重试机制
	var lastErr error
	for i := 0; i < 3; i++ {
		admin, err := sarama.NewClusterAdmin(strings.Split(c.address, ","), config)
		if err != nil {
			lastErr = fmt.Errorf("创建Kafka管理员客户端失败: %w", err)
			if i < 2 {
				time.Sleep(time.Duration(i+1) * time.Second)
				continue
			}
			return nil, lastErr
		}

		// 获取所有消费者组列表
		groups, err := admin.ListConsumerGroups()
		if err != nil {
			admin.Close()
			lastErr = fmt.Errorf("获取消费者组列表失败: %w", err)
			if i < 2 {
				time.Sleep(time.Duration(i+1) * time.Second)
				continue
			}
			return nil, lastErr
		}

		var consumerGroups []KafkaConsumerGroupInfo

		// 获取每个消费者组的详细信息
		for groupID := range groups {
			description, err := admin.DescribeConsumerGroups([]string{groupID})
			if err != nil {
				continue // 跳过无法获取详情的消费者组
			}

			if len(description) == 0 {
				continue
			}

			groupDesc := description[0]

			// 获取消费者组的Topic列表
			topics := make([]string, 0)
			for member := range groupDesc.Members {
				if assignment, err := groupDesc.Members[member].GetMemberAssignment(); err == nil {
					for topic := range assignment.Topics {
						if !stringInSlice(topic, topics) {
							topics = append(topics, topic)
						}
					}
				}
			}

			// 获取消费者组的偏移量信息（暂时注释掉，后续可以扩展）
			// offsets, err := admin.ListConsumerGroupOffsets(groupID, nil)
			// if err != nil {
			// 	offsets = nil
			// }

			// 计算Lag信息（简化处理，实际生产环境需要更复杂的逻辑）
			// lagInfo := make(map[string]int64)

			consumerGroup := KafkaConsumerGroupInfo{
				Name:        groupID,
				State:       groupDesc.State,
				Members:     len(groupDesc.Members),
				Topics:      topics,
				Description: fmt.Sprintf("Kafka消费者组 - %d个成员订阅了%d个Topic", len(groupDesc.Members), len(topics)),
			}

			consumerGroups = append(consumerGroups, consumerGroup)
		}

		admin.Close()
		return consumerGroups, nil
	}

	return nil, lastErr
}

// GetConsumerGroupOffsets 获取消费者组偏移量信息
func (c *KafkaSaramaClient) GetConsumerGroupOffsets(groupID string) (map[string]int64, error) {
	config := c.getSaramaConfig()

	admin, err := sarama.NewClusterAdmin(strings.Split(c.address, ","), config)
	if err != nil {
		return nil, fmt.Errorf("创建Kafka管理员客户端失败: %w", err)
	}
	defer admin.Close()

	offsets, err := admin.ListConsumerGroupOffsets(groupID, nil)
	if err != nil {
		return nil, fmt.Errorf("获取消费者组偏移量失败: %w", err)
	}

	result := make(map[string]int64)
	for topic, partitionOffsets := range offsets.Blocks {
		totalOffset := int64(0)
		for _, block := range partitionOffsets {
			totalOffset += block.Offset
		}
		result[topic] = totalOffset
	}

	return result, nil
}

// stringInSlice 检查字符串是否在切片中
func stringInSlice(str string, slice []string) bool {
	for _, item := range slice {
		if item == str {
			return true
		}
	}
	return false
}

// Close 关闭客户端连接
func (c *KafkaSaramaClient) Close() error {
	// sarama的连接由ClusterAdmin管理，这里不需要额外处理
	return nil
}
