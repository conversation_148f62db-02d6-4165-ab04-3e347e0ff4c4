package impl

import (
	"blops/app/clients"
	"blops/app/models/po"
	"encoding/json"
	"fmt"
	"time"
)

// doKafkaSync 执行Kafka同步
func (svc *SyncService) doKafkaSync(task *po.SyncTask) error {
	// 获取源和目标配置
	sourceConfig := task.SourceConfig
	targetConfig := task.TargetConfig

	// 创建客户端
	sourceClient := clients.NewKafkaClient(sourceConfig.Address, sourceConfig.Username, sourceConfig.Password)
	targetClient := clients.NewKafkaClient(targetConfig.Address, targetConfig.Username, targetConfig.Password)
	defer sourceClient.Close()
	defer targetClient.Close()

	// 获取源集群数据
	sourceTopics, err := sourceClient.GetTopics()
	if err != nil {
		return fmt.Errorf("获取源集群Topic失败: %v", err)
	}

	var totalItems int
	var syncItems []*po.SyncItem

	// 准备Topic同步项目
	if task.SyncTopics {
		for _, topic := range sourceTopics {
			sourceData, _ := json.Marshal(topic)
			syncItems = append(syncItems, &po.SyncItem{
				TaskID:     task.ID,
				ItemType:   "topic",
				ItemName:   topic.Name,
				Status:     "pending",
				SourceData: string(sourceData),
				CreatedAt:  time.Now(),
				UpdatedAt:  time.Now(),
			})
			totalItems++
		}
	}

	// 更新总项目数
	task.TotalItems = totalItems
	svc.UpdateSyncTask(task)

	// 创建同步项目记录
	for _, item := range syncItems {
		svc.db.Create(item)
	}

	// 执行同步
	var completedItems, skippedItems, failedItems int
	for i, item := range syncItems {
		// 更新进度
		progress := int(float64(i) / float64(totalItems) * 100)
		task.Progress = progress
		task.CompletedItems = completedItems
		task.SkippedItems = skippedItems
		task.FailedItems = failedItems
		svc.UpdateSyncTask(task)

		if item.ItemType == "topic" {
			// 解析源数据
			var topicInfo clients.TopicInfo
			json.Unmarshal([]byte(item.SourceData), &topicInfo)

			// 尝试创建Topic
			if task.SkipExisting {
				created, err := targetClient.CreateTopicIfNotExists(topicInfo.Name, topicInfo.Partitions, topicInfo.Replicas)
				if err != nil {
					item.Status = "failed"
					item.ErrorMsg = err.Error()
					failedItems++
					svc.AddSyncLog(&po.SyncLog{
						TaskID:  task.ID,
						Level:   "error",
						Message: fmt.Sprintf("创建Topic失败: %s", topicInfo.Name),
						Details: err.Error(),
					})
				} else if created {
					item.Status = "completed"
					completedItems++
					svc.AddSyncLog(&po.SyncLog{
						TaskID:  task.ID,
						Level:   "info",
						Message: fmt.Sprintf("成功创建Topic: %s", topicInfo.Name),
						Details: fmt.Sprintf("分区数: %d, 副本数: %d", topicInfo.Partitions, topicInfo.Replicas),
					})
				} else {
					item.Status = "skipped"
					skippedItems++
					svc.AddSyncLog(&po.SyncLog{
						TaskID:  task.ID,
						Level:   "info",
						Message: fmt.Sprintf("跳过已存在的Topic: %s", topicInfo.Name),
					})
				}
			} else {
				// 强制创建
				err := targetClient.CreateTopic(topicInfo.Name, topicInfo.Partitions, topicInfo.Replicas)
				if err != nil {
					item.Status = "failed"
					item.ErrorMsg = err.Error()
					failedItems++
					svc.AddSyncLog(&po.SyncLog{
						TaskID:  task.ID,
						Level:   "error",
						Message: fmt.Sprintf("创建Topic失败: %s", topicInfo.Name),
						Details: err.Error(),
					})
				} else {
					item.Status = "completed"
					completedItems++
					svc.AddSyncLog(&po.SyncLog{
						TaskID:  task.ID,
						Level:   "info",
						Message: fmt.Sprintf("成功创建Topic: %s", topicInfo.Name),
						Details: fmt.Sprintf("分区数: %d, 副本数: %d", topicInfo.Partitions, topicInfo.Replicas),
					})
				}
			}

			// 更新同步项目状态
			item.UpdatedAt = time.Now()
			svc.db.Save(item)
		}
	}

	// 更新最终统计
	task.CompletedItems = completedItems
	task.SkippedItems = skippedItems
	task.FailedItems = failedItems
	svc.UpdateSyncTask(task)

	return nil
}

// doRocketMQSync 执行RocketMQ同步
func (svc *SyncService) doRocketMQSync(task *po.SyncTask) error {
	// 获取源和目标配置
	sourceConfig := task.SourceConfig
	targetConfig := task.TargetConfig

	// 创建客户端
	sourceClient := clients.NewRocketMQClient(sourceConfig.Address, sourceConfig.Username, sourceConfig.Password)
	targetClient := clients.NewRocketMQClient(targetConfig.Address, targetConfig.Username, targetConfig.Password)
	defer sourceClient.Close()
	defer targetClient.Close()

	var totalItems int
	var syncItems []*po.SyncItem

	// 准备Topic同步项目
	if task.SyncTopics {
		sourceTopics, err := sourceClient.GetTopics()
		if err != nil {
			return fmt.Errorf("获取源集群Topic失败: %v", err)
		}

		for _, topic := range sourceTopics {
			sourceData, _ := json.Marshal(topic)
			syncItems = append(syncItems, &po.SyncItem{
				TaskID:     task.ID,
				ItemType:   "topic",
				ItemName:   topic.Name,
				Status:     "pending",
				SourceData: string(sourceData),
				CreatedAt:  time.Now(),
				UpdatedAt:  time.Now(),
			})
			totalItems++
		}
	}

	// 准备Group同步项目
	if task.SyncGroups {
		sourceGroups, err := sourceClient.GetSubscriptionGroups()
		if err != nil {
			return fmt.Errorf("获取源集群订阅组失败: %v", err)
		}

		for _, group := range sourceGroups {
			sourceData, _ := json.Marshal(group)
			syncItems = append(syncItems, &po.SyncItem{
				TaskID:     task.ID,
				ItemType:   "group",
				ItemName:   group.Name,
				Status:     "pending",
				SourceData: string(sourceData),
				CreatedAt:  time.Now(),
				UpdatedAt:  time.Now(),
			})
			totalItems++
		}
	}

	// 更新总项目数
	task.TotalItems = totalItems
	svc.UpdateSyncTask(task)

	// 创建同步项目记录
	for _, item := range syncItems {
		svc.db.Create(item)
	}

	// 执行同步
	var completedItems, skippedItems, failedItems int
	for i, item := range syncItems {
		// 更新进度
		progress := int(float64(i) / float64(totalItems) * 100)
		task.Progress = progress
		task.CompletedItems = completedItems
		task.SkippedItems = skippedItems
		task.FailedItems = failedItems
		svc.UpdateSyncTask(task)

		if item.ItemType == "topic" {
			// 解析源数据
			var topicInfo clients.RocketMQTopicInfo
			json.Unmarshal([]byte(item.SourceData), &topicInfo)

			// 尝试创建Topic
			if task.SkipExisting {
				created, err := targetClient.CreateTopicIfNotExists(topicInfo.Name, topicInfo.QueueNum)
				if err != nil {
					item.Status = "failed"
					item.ErrorMsg = err.Error()
					failedItems++
					svc.AddSyncLog(&po.SyncLog{
						TaskID:  task.ID,
						Level:   "error",
						Message: fmt.Sprintf("创建Topic失败: %s", topicInfo.Name),
						Details: err.Error(),
					})
				} else if created {
					item.Status = "completed"
					completedItems++
					svc.AddSyncLog(&po.SyncLog{
						TaskID:  task.ID,
						Level:   "info",
						Message: fmt.Sprintf("成功创建Topic: %s", topicInfo.Name),
						Details: fmt.Sprintf("队列数: %d", topicInfo.QueueNum),
					})
				} else {
					item.Status = "skipped"
					skippedItems++
					svc.AddSyncLog(&po.SyncLog{
						TaskID:  task.ID,
						Level:   "info",
						Message: fmt.Sprintf("跳过已存在的Topic: %s", topicInfo.Name),
					})
				}
			} else {
				// 强制创建
				err := targetClient.CreateTopic(topicInfo.Name, topicInfo.QueueNum)
				if err != nil {
					item.Status = "failed"
					item.ErrorMsg = err.Error()
					failedItems++
					svc.AddSyncLog(&po.SyncLog{
						TaskID:  task.ID,
						Level:   "error",
						Message: fmt.Sprintf("创建Topic失败: %s", topicInfo.Name),
						Details: err.Error(),
					})
				} else {
					item.Status = "completed"
					completedItems++
					svc.AddSyncLog(&po.SyncLog{
						TaskID:  task.ID,
						Level:   "info",
						Message: fmt.Sprintf("成功创建Topic: %s", topicInfo.Name),
						Details: fmt.Sprintf("队列数: %d", topicInfo.QueueNum),
					})
				}
			}

			// 更新同步项目状态
			item.UpdatedAt = time.Now()
			svc.db.Save(item)

		} else if item.ItemType == "group" {
			// 解析源数据
			var groupInfo clients.RocketMQGroupInfo
			json.Unmarshal([]byte(item.SourceData), &groupInfo)

			// 尝试创建订阅组
			if task.SkipExisting {
				created, err := targetClient.CreateSubscriptionGroupIfNotExists(groupInfo.Name)
				if err != nil {
					item.Status = "failed"
					item.ErrorMsg = err.Error()
					failedItems++
					svc.AddSyncLog(&po.SyncLog{
						TaskID:  task.ID,
						Level:   "error",
						Message: fmt.Sprintf("创建订阅组失败: %s", groupInfo.Name),
						Details: err.Error(),
					})
				} else if created {
					item.Status = "completed"
					completedItems++
					svc.AddSyncLog(&po.SyncLog{
						TaskID:  task.ID,
						Level:   "info",
						Message: fmt.Sprintf("成功创建订阅组: %s", groupInfo.Name),
					})
				} else {
					item.Status = "skipped"
					skippedItems++
					svc.AddSyncLog(&po.SyncLog{
						TaskID:  task.ID,
						Level:   "info",
						Message: fmt.Sprintf("跳过已存在的订阅组: %s", groupInfo.Name),
					})
				}
			} else {
				// 强制创建
				err := targetClient.CreateSubscriptionGroup(groupInfo.Name)
				if err != nil {
					item.Status = "failed"
					item.ErrorMsg = err.Error()
					failedItems++
					svc.AddSyncLog(&po.SyncLog{
						TaskID:  task.ID,
						Level:   "error",
						Message: fmt.Sprintf("创建订阅组失败: %s", groupInfo.Name),
						Details: err.Error(),
					})
				} else {
					item.Status = "completed"
					completedItems++
					svc.AddSyncLog(&po.SyncLog{
						TaskID:  task.ID,
						Level:   "info",
						Message: fmt.Sprintf("成功创建订阅组: %s", groupInfo.Name),
					})
				}
			}

			// 更新同步项目状态
			item.UpdatedAt = time.Now()
			svc.db.Save(item)
		}
	}

	// 更新最终统计
	task.CompletedItems = completedItems
	task.SkippedItems = skippedItems
	task.FailedItems = failedItems
	svc.UpdateSyncTask(task)

	return nil
}
