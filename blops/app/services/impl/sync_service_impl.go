package impl

import (
	"blops/app/models/dto"
	"blops/app/models/po"
	"blops/app/services"
	"blops/sql"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// SyncService 同步服务实现
type SyncService struct {
	db            *gorm.DB
	middlewareSvc services.MiddlewareServiceInf
}

// 全局同步服务实例
var SyncSvc *SyncService

func init() {
	SyncSvc = &SyncService{
		db:            sql.GDB,
		middlewareSvc: MiddlewareSvc,
	}
}

// NewSyncService 创建同步服务实例
func NewSyncService(db *gorm.DB, middlewareSvc services.MiddlewareServiceInf) *SyncService {
	return &SyncService{
		db:            db,
		middlewareSvc: middlewareSvc,
	}
}

// =============== 同步任务管理 ===============

// CreateSyncTask 创建同步任务
func (svc *SyncService) CreateSyncTask(task *po.SyncTask) error {
	task.CreatedAt = time.Now()
	task.UpdatedAt = time.Now()
	return svc.db.Create(task).Error
}

// GetSyncTask 获取同步任务
func (svc *SyncService) GetSyncTask(id int64) (*po.SyncTask, error) {
	var task po.SyncTask
	err := svc.db.Preload("SourceConfig").Preload("TargetConfig").First(&task, id).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// ListSyncTasks 获取同步任务列表
func (svc *SyncService) ListSyncTasks() ([]*po.SyncTask, error) {
	var tasks []*po.SyncTask
	err := svc.db.Preload("SourceConfig").Preload("TargetConfig").Order("created_at DESC").Find(&tasks).Error
	return tasks, err
}

// UpdateSyncTask 更新同步任务
func (svc *SyncService) UpdateSyncTask(task *po.SyncTask) error {
	task.UpdatedAt = time.Now()
	return svc.db.Save(task).Error
}

// DeleteSyncTask 删除同步任务
func (svc *SyncService) DeleteSyncTask(id int64) error {
	return svc.db.Transaction(func(tx *gorm.DB) error {
		// 删除相关的同步项目
		if err := tx.Where("task_id = ?", id).Delete(&po.SyncItem{}).Error; err != nil {
			return err
		}
		// 删除相关的同步日志
		if err := tx.Where("task_id = ?", id).Delete(&po.SyncLog{}).Error; err != nil {
			return err
		}
		// 删除同步任务
		return tx.Delete(&po.SyncTask{}, id).Error
	})
}

// CancelSyncTask 取消同步任务
func (svc *SyncService) CancelSyncTask(id int64) error {
	return svc.db.Model(&po.SyncTask{}).Where("id = ?", id).Updates(map[string]interface{}{
		"status":       "cancelled",
		"updated_at":   time.Now(),
		"completed_at": time.Now(),
	}).Error
}

// =============== 同步预览 ===============

// PreviewSync 同步预览
func (svc *SyncService) PreviewSync(req *dto.SyncPreviewRequest) (*dto.SyncPreviewResponse, error) {
	// 获取源配置
	sourceConfig, err := svc.middlewareSvc.GetTopicConfig(req.SourceConfigID)
	if err != nil {
		return nil, fmt.Errorf("获取源配置失败: %v", err)
	}

	// 获取目标配置
	targetConfig, err := svc.middlewareSvc.GetTopicConfig(req.TargetConfigID)
	if err != nil {
		return nil, fmt.Errorf("获取目标配置失败: %v", err)
	}

	// 检查配置类型是否一致
	if sourceConfig.Type != targetConfig.Type {
		return &dto.SyncPreviewResponse{
			SourceConfig: dto.ToTopicConfigResponse(sourceConfig),
			TargetConfig: dto.ToTopicConfigResponse(targetConfig),
			CanProceed:   false,
			ErrorMessage: "源配置和目标配置类型不一致",
		}, nil
	}

	// 创建响应对象
	sourceConfigResp := dto.ToTopicConfigResponse(sourceConfig)
	targetConfigResp := dto.ToTopicConfigResponse(targetConfig)
	
	response := &dto.SyncPreviewResponse{
		SourceConfig: sourceConfigResp,
		TargetConfig: targetConfigResp,
		CanProceed:   true,
	}

	// 根据配置类型进行预览
	switch sourceConfig.Type {
	case "kafka":
		return svc.previewKafkaSync(sourceConfig, targetConfig, req, response)
	case "rocketmq":
		return svc.previewRocketMQSync(sourceConfig, targetConfig, req, response)
	default:
		response.CanProceed = false
		response.ErrorMessage = fmt.Sprintf("不支持的配置类型: %s", sourceConfig.Type)
		return response, nil
	}
}

// previewKafkaSync Kafka同步预览
func (svc *SyncService) previewKafkaSync(sourceConfig, targetConfig *po.TopicConfig, req *dto.SyncPreviewRequest, response *dto.SyncPreviewResponse) (*dto.SyncPreviewResponse, error) {
	// 获取源集群数据
	sourceData, err := svc.middlewareSvc.GetTopicsAndGroups(sourceConfig.ID)
	if err != nil {
		response.CanProceed = false
		response.ErrorMessage = fmt.Sprintf("获取源集群数据失败: %v", err)
		return response, nil
	}

	// 获取目标集群数据
	targetData, err := svc.middlewareSvc.GetTopicsAndGroups(targetConfig.ID)
	if err != nil {
		response.CanProceed = false
		response.ErrorMessage = fmt.Sprintf("获取目标集群数据失败: %v", err)
		return response, nil
	}

	// 填充源集群统计数据
	sourceTopicCount := len(sourceData.Topics)
	sourceGroupCount := len(sourceData.Groups)
	response.SourceConfig.TopicCount = &sourceTopicCount
	response.SourceConfig.GroupCount = &sourceGroupCount

	// 填充目标集群统计数据
	targetTopicCount := len(targetData.Topics)
	targetGroupCount := len(targetData.Groups)
	response.TargetConfig.TopicCount = &targetTopicCount
	response.TargetConfig.GroupCount = &targetGroupCount

	// 比较Topic差异
	if req.SyncTopics {
		topicsToSync, topicsToSkip := svc.compareTopics(sourceData.Topics, targetData.Topics)
		response.TopicsToSync = topicsToSync
		response.TopicsToSkip = topicsToSkip
	}

	// 比较Group差异（Kafka的Group支持有限）
	if req.SyncGroups {
		groupsToSync, groupsToSkip := svc.compareGroups(sourceData.Groups, targetData.Groups)
		response.GroupsToSync = groupsToSync
		response.GroupsToSkip = groupsToSkip
	}

	// 计算统计信息
	response.TotalItems = len(response.TopicsToSync) + len(response.TopicsToSkip) + len(response.GroupsToSync) + len(response.GroupsToSkip)
	response.ItemsToSync = len(response.TopicsToSync) + len(response.GroupsToSync)
	response.ItemsToSkip = len(response.TopicsToSkip) + len(response.GroupsToSkip)

	return response, nil
}

// previewRocketMQSync RocketMQ同步预览
func (svc *SyncService) previewRocketMQSync(sourceConfig, targetConfig *po.TopicConfig, req *dto.SyncPreviewRequest, response *dto.SyncPreviewResponse) (*dto.SyncPreviewResponse, error) {
	// 获取源集群数据
	sourceData, err := svc.middlewareSvc.GetTopicsAndGroups(sourceConfig.ID)
	if err != nil {
		response.CanProceed = false
		response.ErrorMessage = fmt.Sprintf("获取源集群数据失败: %v", err)
		return response, nil
	}

	// 获取目标集群数据
	targetData, err := svc.middlewareSvc.GetTopicsAndGroups(targetConfig.ID)
	if err != nil {
		response.CanProceed = false
		response.ErrorMessage = fmt.Sprintf("获取目标集群数据失败: %v", err)
		return response, nil
	}

	// 填充源集群统计数据
	sourceTopicCount := len(sourceData.Topics)
	sourceGroupCount := len(sourceData.Groups)
	response.SourceConfig.TopicCount = &sourceTopicCount
	response.SourceConfig.GroupCount = &sourceGroupCount

	// 填充目标集群统计数据
	targetTopicCount := len(targetData.Topics)
	targetGroupCount := len(targetData.Groups)
	response.TargetConfig.TopicCount = &targetTopicCount
	response.TargetConfig.GroupCount = &targetGroupCount

	// 比较Topic差异
	if req.SyncTopics {
		topicsToSync, topicsToSkip := svc.compareTopics(sourceData.Topics, targetData.Topics)
		response.TopicsToSync = topicsToSync
		response.TopicsToSkip = topicsToSkip
	}

	// 比较Group差异
	if req.SyncGroups {
		groupsToSync, groupsToSkip := svc.compareGroups(sourceData.Groups, targetData.Groups)
		response.GroupsToSync = groupsToSync
		response.GroupsToSkip = groupsToSkip
	}

	// 计算统计信息
	response.TotalItems = len(response.TopicsToSync) + len(response.TopicsToSkip) + len(response.GroupsToSync) + len(response.GroupsToSkip)
	response.ItemsToSync = len(response.TopicsToSync) + len(response.GroupsToSync)
	response.ItemsToSkip = len(response.TopicsToSkip) + len(response.GroupsToSkip)

	return response, nil
}

// compareTopics 比较Topic差异
func (svc *SyncService) compareTopics(sourceTopics, targetTopics []dto.TopicInfo) (toSync, toSkip []dto.TopicInfo) {
	targetTopicMap := make(map[string]dto.TopicInfo)
	for _, topic := range targetTopics {
		targetTopicMap[topic.Name] = topic
	}

	for _, sourceTopic := range sourceTopics {
		if _, exists := targetTopicMap[sourceTopic.Name]; exists {
			toSkip = append(toSkip, sourceTopic)
		} else {
			toSync = append(toSync, sourceTopic)
		}
	}

	return toSync, toSkip
}

// compareGroups 比较Group差异
func (svc *SyncService) compareGroups(sourceGroups, targetGroups []dto.GroupInfo) (toSync, toSkip []dto.GroupInfo) {
	targetGroupMap := make(map[string]dto.GroupInfo)
	for _, group := range targetGroups {
		targetGroupMap[group.Name] = group
	}

	for _, sourceGroup := range sourceGroups {
		if _, exists := targetGroupMap[sourceGroup.Name]; exists {
			toSkip = append(toSkip, sourceGroup)
		} else {
			toSync = append(toSync, sourceGroup)
		}
	}

	return toSync, toSkip
}

// =============== 执行同步 ===============

// ExecuteSync 执行同步
func (svc *SyncService) ExecuteSync(taskID int64) error {
	// 获取任务信息
	task, err := svc.GetSyncTask(taskID)
	if err != nil {
		return fmt.Errorf("获取任务失败: %v", err)
	}

	// 检查任务状态
	if task.Status != "pending" {
		return fmt.Errorf("任务状态不正确，当前状态: %s", task.Status)
	}

	// 更新任务状态为运行中
	task.Status = "running"
	now := time.Now()
	task.StartedAt = &now
	task.UpdatedAt = now
	if err := svc.UpdateSyncTask(task); err != nil {
		return fmt.Errorf("更新任务状态失败: %v", err)
	}

	// 添加开始日志
	svc.AddSyncLog(&po.SyncLog{
		TaskID:  taskID,
		Level:   "info",
		Message: "开始执行同步任务",
		Details: fmt.Sprintf("任务ID: %d, 任务名称: %s", taskID, task.Name),
	})

	// 异步执行同步
	go func() {
		err := svc.doSync(task)
		if err != nil {
			// 更新任务状态为失败
			task.Status = "failed"
			task.ErrorMessage = err.Error()
			completedAt := time.Now()
			task.CompletedAt = &completedAt
			task.UpdatedAt = completedAt
			svc.UpdateSyncTask(task)

			// 添加错误日志
			svc.AddSyncLog(&po.SyncLog{
				TaskID:  taskID,
				Level:   "error",
				Message: "同步任务执行失败",
				Details: err.Error(),
			})
		} else {
			// 更新任务状态为完成
			task.Status = "completed"
			task.Progress = 100
			completedAt := time.Now()
			task.CompletedAt = &completedAt
			task.UpdatedAt = completedAt
			svc.UpdateSyncTask(task)

			// 添加完成日志
			svc.AddSyncLog(&po.SyncLog{
				TaskID:  taskID,
				Level:   "info",
				Message: "同步任务执行完成",
				Details: fmt.Sprintf("总计: %d, 完成: %d, 跳过: %d, 失败: %d",
					task.TotalItems, task.CompletedItems, task.SkippedItems, task.FailedItems),
			})
		}
	}()

	return nil
}

// doSync 执行具体的同步逻辑
func (svc *SyncService) doSync(task *po.SyncTask) error {
	// 根据配置类型执行不同的同步逻辑
	switch task.Type {
	case "kafka":
		return svc.doKafkaSync(task)
	case "rocketmq":
		return svc.doRocketMQSync(task)
	default:
		return fmt.Errorf("不支持的同步类型: %s", task.Type)
	}
}

// =============== 同步项目管理 ===============

// GetSyncItems 获取同步项目列表
func (svc *SyncService) GetSyncItems(taskID int64) ([]*po.SyncItem, error) {
	var items []*po.SyncItem
	err := svc.db.Where("task_id = ?", taskID).Order("created_at ASC").Find(&items).Error
	return items, err
}

// UpdateSyncItem 更新同步项目
func (svc *SyncService) UpdateSyncItem(item *po.SyncItem) error {
	item.UpdatedAt = time.Now()
	return svc.db.Save(item).Error
}

// =============== 同步日志管理 ===============

// GetSyncLogs 获取同步日志列表
func (svc *SyncService) GetSyncLogs(taskID int64) ([]*po.SyncLog, error) {
	var logs []*po.SyncLog
	err := svc.db.Where("task_id = ?", taskID).Order("created_at ASC").Find(&logs).Error
	return logs, err
}

// AddSyncLog 添加同步日志
func (svc *SyncService) AddSyncLog(log *po.SyncLog) error {
	log.CreatedAt = time.Now()
	return svc.db.Create(log).Error
}

// =============== 获取同步统计信息 ===============

// GetSyncStats 获取同步统计信息
func (svc *SyncService) GetSyncStats(taskID int64) (*dto.SyncTaskResponse, error) {
	task, err := svc.GetSyncTask(taskID)
	if err != nil {
		return nil, err
	}
	return dto.ToSyncTaskResponse(task), nil
}
