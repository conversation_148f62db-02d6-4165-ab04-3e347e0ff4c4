package services

import (
	"blops/app/models/dto"
	"blops/app/models/po"
)

// MiddlewareServiceInf 中间件服务接口
type MiddlewareServiceInf interface {
	// 环境相关接口
	GetEnv(id int64) (*po.MiddlewareEnv, error)
	ListEnvs() ([]*po.MiddlewareEnv, error)
	CreateEnv(env *po.MiddlewareEnv) error
	UpdateEnv(env *po.MiddlewareEnv) error
	DeleteEnv(id int64) error

	// 链接相关接口
	GetLink(id int64) (*po.MiddlewareLink, error)
	ListLinks() ([]*po.MiddlewareLink, error)
	ListLinksByType(linkType string) ([]*po.MiddlewareLink, error)
	ListLinksByEnv(envID int64) ([]*po.MiddlewareLink, error)
	ListLinksByTypeAndEnv(linkType string, envID int64) ([]*po.MiddlewareLink, error)
	ListLinksWithEnvInfo() ([]*po.MiddlewareLink, error)
	ListLinksByTypeWithEnvInfo(linkType string) ([]*po.MiddlewareLink, error)
	CreateLink(link *po.MiddlewareLink) error
	UpdateLink(link *po.MiddlewareLink) error
	DeleteLink(id int64) error

	// Topic配置相关接口
	GetTopicConfig(id int64) (*po.TopicConfig, error)
	ListTopicConfigs() ([]*po.TopicConfig, error)
	ListTopicConfigsByType(configType string) ([]*po.TopicConfig, error)
	ListTopicConfigsByEnv(envID int64) ([]*po.TopicConfig, error)
	ListTopicConfigsByTypeAndEnv(configType string, envID int64) ([]*po.TopicConfig, error)
	ListTopicConfigsWithEnvInfo() ([]*po.TopicConfig, error)
	ListTopicConfigsByTypeWithEnvInfo(configType string) ([]*po.TopicConfig, error)
	CreateTopicConfig(config *po.TopicConfig) error
	UpdateTopicConfig(config *po.TopicConfig) error
	DeleteTopicConfig(id int64) error

	// Topic和Group查询接口
	GetTopicsAndGroups(configID int64) (*dto.TopicListResponse, error)
	GetTopicsAndGroupsFromCache(configID int64) (*dto.TopicListResponse, error)
	RefreshTopicsAndGroupsCache(configID int64) (*dto.TopicListResponse, error)

	// 缓存管理接口
	GetCacheMeta(configID int64) (*po.TopicGroupCacheMeta, error)
	UpdateCacheMeta(meta *po.TopicGroupCacheMeta) error
	ClearCache(configID int64) error
}
