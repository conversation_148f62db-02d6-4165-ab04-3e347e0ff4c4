package services

import (
	"blops/app/models/dto"
	"blops/app/models/po"
)

// SyncServiceInf 同步服务接口
type SyncServiceInf interface {
	// 同步任务管理
	CreateSyncTask(task *po.SyncTask) error
	GetSyncTask(id int64) (*po.SyncTask, error)
	ListSyncTasks() ([]*po.SyncTask, error)
	UpdateSyncTask(task *po.SyncTask) error
	DeleteSyncTask(id int64) error
	CancelSyncTask(id int64) error

	// 同步预览
	PreviewSync(req *dto.SyncPreviewRequest) (*dto.SyncPreviewResponse, error)

	// 执行同步
	ExecuteSync(taskID int64) error

	// 同步项目管理
	GetSyncItems(taskID int64) ([]*po.SyncItem, error)
	UpdateSyncItem(item *po.SyncItem) error

	// 同步日志管理
	GetSyncLogs(taskID int64) ([]*po.SyncLog, error)
	AddSyncLog(log *po.SyncLog) error

	// 获取同步统计信息
	GetSyncStats(taskID int64) (*dto.SyncTaskResponse, error)
}
