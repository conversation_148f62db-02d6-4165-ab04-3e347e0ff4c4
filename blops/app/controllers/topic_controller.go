package controllers

import (
	"blops/app/clients"
	"blops/app/models/dto"
	. "blops/app/services"
	. "blops/app/services/impl"
	. "blops/enums/http"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// TopicController Topic管理控制器
type TopicController struct {
	middlewareSvc MiddlewareServiceInf
}

// 全局Topic控制器实例
var TopicCtrl *TopicController

func init() {
	TopicCtrl = &TopicController{
		middlewareSvc: MiddlewareSvc,
	}
}

// =============== Topic配置相关接口 ===============

// GetTopicConfig 获取单个Topic配置
func (ctrl *TopicController) GetTopicConfig(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSO<PERSON>(http.StatusBadRequest, BadRequest(Options{Msg: "无效的ID参数"}))
		return
	}

	config, err := ctrl.middlewareSvc.GetTopicConfig(id)
	if err != nil {
		c.JSON(http.StatusNotFound, NotFound(Options{Msg: "Topic配置不存在"}))
		return
	}

	statusCode, response := Success(dto.ToTopicConfigResponse(config))
	c.JSON(statusCode, response)
}

// ListTopicConfigs 获取Topic配置列表
func (ctrl *TopicController) ListTopicConfigs(c *gin.Context) {
	withEnvInfo := c.Query("with_env_info") == "true"

	var configs []*dto.TopicConfigResponse

	if withEnvInfo {
		configsData, err := ctrl.middlewareSvc.ListTopicConfigsWithEnvInfo()
		if err != nil {
			c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "获取Topic配置列表失败"}))
			return
		}

		configs = make([]*dto.TopicConfigResponse, 0, len(configsData))
		for _, config := range configsData {
			configs = append(configs, dto.ToTopicConfigResponse(config))
		}
	} else {
		configsData, err := ctrl.middlewareSvc.ListTopicConfigs()
		if err != nil {
			c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "获取Topic配置列表失败"}))
			return
		}

		configs = make([]*dto.TopicConfigResponse, 0, len(configsData))
		for _, config := range configsData {
			configs = append(configs, dto.ToTopicConfigResponse(config))
		}
	}

	statusCode, response := Success(configs)
	c.JSON(statusCode, response)
}

// CreateTopicConfig 创建Topic配置
func (ctrl *TopicController) CreateTopicConfig(c *gin.Context) {
	var req dto.TopicConfigCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的请求参数"}))
		return
	}

	// 检查环境是否存在
	_, err := ctrl.middlewareSvc.GetEnv(req.Env)
	if err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "所选环境不存在"}))
		return
	}

	config := req.ToTopicConfigPO()
	if err := ctrl.middlewareSvc.CreateTopicConfig(config); err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "创建Topic配置失败"}))
		return
	}

	statusCode, response := Success(dto.ToTopicConfigResponse(config))
	c.JSON(statusCode, response)
}

// UpdateTopicConfig 更新Topic配置
func (ctrl *TopicController) UpdateTopicConfig(c *gin.Context) {
	var req dto.TopicConfigUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的请求参数"}))
		return
	}

	// 检查配置是否存在
	_, err := ctrl.middlewareSvc.GetTopicConfig(req.ID)
	if err != nil {
		c.JSON(http.StatusNotFound, NotFound(Options{Msg: "Topic配置不存在"}))
		return
	}

	// 检查环境是否存在
	_, err = ctrl.middlewareSvc.GetEnv(req.Env)
	if err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "所选环境不存在"}))
		return
	}

	config := req.ToTopicConfigPO()
	if err := ctrl.middlewareSvc.UpdateTopicConfig(config); err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "更新Topic配置失败"}))
		return
	}

	statusCode, response := Success(dto.ToTopicConfigResponse(config))
	c.JSON(statusCode, response)
}

// DeleteTopicConfig 删除Topic配置
func (ctrl *TopicController) DeleteTopicConfig(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的ID参数"}))
		return
	}

	// 检查配置是否存在
	_, err = ctrl.middlewareSvc.GetTopicConfig(id)
	if err != nil {
		c.JSON(http.StatusNotFound, NotFound(Options{Msg: "Topic配置不存在"}))
		return
	}

	if err := ctrl.middlewareSvc.DeleteTopicConfig(id); err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "删除Topic配置失败"}))
		return
	}

	statusCode, response := Success(nil)
	c.JSON(statusCode, response)
}

// =============== Topic和Group查询接口 ===============

// GetTopicsAndGroups 获取Topic和Group信息
func (ctrl *TopicController) GetTopicsAndGroups(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的ID参数"}))
		return
	}

	result, err := ctrl.middlewareSvc.GetTopicsAndGroups(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: err.Error()}))
		return
	}

	statusCode, response := Success(result)
	c.JSON(statusCode, response)
}

// RefreshTopicsAndGroups 刷新Topic和Group信息
func (ctrl *TopicController) RefreshTopicsAndGroups(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的ID参数"}))
		return
	}

	result, err := ctrl.middlewareSvc.RefreshTopicsAndGroupsCache(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: err.Error()}))
		return
	}

	statusCode, response := Success(result)
	c.JSON(statusCode, response)
}

// ListTopicConfigsByType 按类型获取Topic配置列表
func (ctrl *TopicController) ListTopicConfigsByType(c *gin.Context) {
	var req dto.MiddlewareLinkTypeFilterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的请求参数"}))
		return
	}

	withEnvInfo := c.Query("with_env_info") == "true"
	var configs []*dto.TopicConfigResponse

	if withEnvInfo {
		configsData, err := ctrl.middlewareSvc.ListTopicConfigsByTypeWithEnvInfo(req.Type)
		if err != nil {
			c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "获取Topic配置列表失败"}))
			return
		}

		configs = make([]*dto.TopicConfigResponse, 0, len(configsData))
		for _, config := range configsData {
			configs = append(configs, dto.ToTopicConfigResponse(config))
		}
	} else {
		configsData, err := ctrl.middlewareSvc.ListTopicConfigsByType(req.Type)
		if err != nil {
			c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "获取Topic配置列表失败"}))
			return
		}

		configs = make([]*dto.TopicConfigResponse, 0, len(configsData))
		for _, config := range configsData {
			configs = append(configs, dto.ToTopicConfigResponse(config))
		}
	}

	statusCode, response := Success(configs)
	c.JSON(statusCode, response)
}

// ListTopicConfigsByEnv 按环境获取Topic配置列表
func (ctrl *TopicController) ListTopicConfigsByEnv(c *gin.Context) {
	var req dto.MiddlewareLinkEnvFilterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的请求参数"}))
		return
	}

	configsData, err := ctrl.middlewareSvc.ListTopicConfigsByEnv(req.Env)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "获取Topic配置列表失败"}))
		return
	}

	configs := make([]*dto.TopicConfigResponse, 0, len(configsData))
	for _, config := range configsData {
		configs = append(configs, dto.ToTopicConfigResponse(config))
	}

	statusCode, response := Success(configs)
	c.JSON(statusCode, response)
}

// ListTopicConfigsByTypeAndEnv 按类型和环境获取Topic配置列表
func (ctrl *TopicController) ListTopicConfigsByTypeAndEnv(c *gin.Context) {
	var req dto.MiddlewareLinkTypeAndEnvFilterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的请求参数"}))
		return
	}

	configsData, err := ctrl.middlewareSvc.ListTopicConfigsByTypeAndEnv(req.Type, req.Env)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "获取Topic配置列表失败"}))
		return
	}

	configs := make([]*dto.TopicConfigResponse, 0, len(configsData))
	for _, config := range configsData {
		configs = append(configs, dto.ToTopicConfigResponse(config))
	}

	statusCode, response := Success(configs)
	c.JSON(statusCode, response)
}

// TestTopicConfigConnection 测试Topic配置连接
func (ctrl *TopicController) TestTopicConfigConnection(c *gin.Context) {
	var req dto.TopicConfigCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的请求参数"}))
		return
	}

	var testErr error
	switch req.Type {
	case "kafka":
		kafkaClient := clients.NewKafkaSaramaClient(req.Address, req.Username, req.Password)
		defer kafkaClient.Close()
		testErr = kafkaClient.TestConnection()
	case "rocketmq":
		rocketMQClient := clients.NewRocketMQClient(req.Address, req.Username, req.Password)
		defer rocketMQClient.Close()
		testErr = rocketMQClient.TestConnection()
	default:
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "不支持的配置类型"}))
		return
	}

	if testErr != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "连接测试失败: " + testErr.Error()}))
		return
	}

	statusCode, response := Success(map[string]string{"message": "连接测试成功"})
	c.JSON(statusCode, response)
}
