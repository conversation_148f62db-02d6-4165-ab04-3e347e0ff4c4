package controllers

import (
	"blops/app/models/dto"
	"blops/app/services"
	"blops/app/services/impl"
	. "blops/enums/http"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// SyncController 同步控制器
type SyncController struct {
	syncSvc services.SyncServiceInf
}

// 全局同步控制器实例
var SyncCtrl *SyncController

func init() {
	SyncCtrl = &SyncController{
		syncSvc: impl.SyncSvc,
	}
}

// NewSyncController 创建同步控制器实例
func NewSyncController(syncSvc services.SyncServiceInf) *SyncController {
	return &SyncController{
		syncSvc: syncSvc,
	}
}

// =============== 同步任务管理接口 ===============

// CreateSyncTask 创建同步任务
func (ctrl *SyncController) CreateSyncTask(c *gin.Context) {
	var req dto.SyncTaskCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的请求参数"}))
		return
	}

	// 验证源配置和目标配置不能相同
	if req.SourceConfigID == req.TargetConfigID {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "源配置和目标配置不能相同"}))
		return
	}

	// 验证至少选择一种同步类型
	if !req.SyncTopics && !req.SyncGroups {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "至少需要选择同步Topic或Group"}))
		return
	}

	task := req.ToSyncTaskPO()
	if err := ctrl.syncSvc.CreateSyncTask(task); err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "创建同步任务失败"}))
		return
	}

	statusCode, response := Success(dto.ToSyncTaskResponse(task))
	c.JSON(statusCode, response)
}

// GetSyncTask 获取单个同步任务
func (ctrl *SyncController) GetSyncTask(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的ID参数"}))
		return
	}

	task, err := ctrl.syncSvc.GetSyncTask(id)
	if err != nil {
		c.JSON(http.StatusNotFound, NotFound(Options{Msg: "同步任务不存在"}))
		return
	}

	statusCode, response := Success(dto.ToSyncTaskResponse(task))
	c.JSON(statusCode, response)
}

// ListSyncTasks 获取同步任务列表
func (ctrl *SyncController) ListSyncTasks(c *gin.Context) {
	tasks, err := ctrl.syncSvc.ListSyncTasks()
	if err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "获取同步任务列表失败"}))
		return
	}

	var responses []*dto.SyncTaskResponse
	for _, task := range tasks {
		responses = append(responses, dto.ToSyncTaskResponse(task))
	}

	statusCode, response := Success(responses)
	c.JSON(statusCode, response)
}

// DeleteSyncTask 删除同步任务
func (ctrl *SyncController) DeleteSyncTask(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的ID参数"}))
		return
	}

	if err := ctrl.syncSvc.DeleteSyncTask(id); err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "删除同步任务失败"}))
		return
	}

	statusCode, response := Success(nil)
	c.JSON(statusCode, response)
}

// CancelSyncTask 取消同步任务
func (ctrl *SyncController) CancelSyncTask(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的ID参数"}))
		return
	}

	if err := ctrl.syncSvc.CancelSyncTask(id); err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "取消同步任务失败"}))
		return
	}

	statusCode, response := Success(nil)
	c.JSON(statusCode, response)
}

// =============== 同步预览和执行接口 ===============

// PreviewSync 同步预览
func (ctrl *SyncController) PreviewSync(c *gin.Context) {
	var req dto.SyncPreviewRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的请求参数"}))
		return
	}

	// 验证源配置和目标配置不能相同
	if req.SourceConfigID == req.TargetConfigID {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "源配置和目标配置不能相同"}))
		return
	}

	// 验证至少选择一种同步类型
	if !req.SyncTopics && !req.SyncGroups {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "至少需要选择同步Topic或Group"}))
		return
	}

	preview, err := ctrl.syncSvc.PreviewSync(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: err.Error()}))
		return
	}

	statusCode, response := Success(preview)
	c.JSON(statusCode, response)
}

// ExecuteSync 执行同步
func (ctrl *SyncController) ExecuteSync(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的ID参数"}))
		return
	}

	if err := ctrl.syncSvc.ExecuteSync(id); err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: err.Error()}))
		return
	}

	statusCode, response := Success(map[string]string{"message": "同步任务已开始执行"})
	c.JSON(statusCode, response)
}

// =============== 同步项目和日志接口 ===============

// GetSyncItems 获取同步项目列表
func (ctrl *SyncController) GetSyncItems(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的ID参数"}))
		return
	}

	items, err := ctrl.syncSvc.GetSyncItems(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "获取同步项目列表失败"}))
		return
	}

	var responses []*dto.SyncItemResponse
	for _, item := range items {
		responses = append(responses, dto.ToSyncItemResponse(item))
	}

	statusCode, response := Success(responses)
	c.JSON(statusCode, response)
}

// GetSyncLogs 获取同步日志列表
func (ctrl *SyncController) GetSyncLogs(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的ID参数"}))
		return
	}

	logs, err := ctrl.syncSvc.GetSyncLogs(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "获取同步日志列表失败"}))
		return
	}

	var responses []*dto.SyncLogResponse
	for _, log := range logs {
		responses = append(responses, dto.ToSyncLogResponse(log))
	}

	statusCode, response := Success(responses)
	c.JSON(statusCode, response)
}

// GetSyncStats 获取同步统计信息
func (ctrl *SyncController) GetSyncStats(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的ID参数"}))
		return
	}

	stats, err := ctrl.syncSvc.GetSyncStats(id)
	if err != nil {
		c.JSON(http.StatusNotFound, NotFound(Options{Msg: "同步任务不存在"}))
		return
	}

	statusCode, response := Success(stats)
	c.JSON(statusCode, response)
}
