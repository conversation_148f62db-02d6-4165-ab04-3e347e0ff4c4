package test

import (
	"blops/app/clients"
	"fmt"
	"testing"
)

// TestKafkaClient 测试Kafka客户端
func TestKafkaClient(t *testing.T) {
	// 注意：这个测试需要真实的Kafka服务器
	// 如果没有Kafka服务器，这个测试会失败
	kafkaAddr := "localhost:9092"
	
	client := clients.NewKafkaClient(kafkaAddr, "", "")
	defer client.Close()
	
	// 测试连接
	err := client.TestConnection()
	if err != nil {
		t.Logf("Kafka连接测试失败 (这是正常的，如果没有运行Kafka服务器): %v", err)
		return
	}
	
	// 如果连接成功，测试获取Topic列表
	topics, err := client.GetTopics()
	if err != nil {
		t.Errorf("获取Kafka Topic列表失败: %v", err)
		return
	}
	
	fmt.Printf("找到 %d 个Kafka Topics:\n", len(topics))
	for _, topic := range topics {
		fmt.Printf("- %s (分区: %d, 副本: %d)\n", topic.Name, topic.Partitions, topic.Replicas)
	}
}

// TestRocketMQClient 测试RocketMQ客户端
func TestRocketMQClient(t *testing.T) {
	// 注意：这个测试需要真实的RocketMQ服务器
	// 如果没有RocketMQ服务器，这个测试会失败
	nameServerAddr := "localhost:9876"
	
	client := clients.NewRocketMQClient(nameServerAddr, "", "")
	defer client.Close()
	
	// 测试连接
	err := client.TestConnection()
	if err != nil {
		t.Logf("RocketMQ连接测试失败 (这是正常的，如果没有运行RocketMQ服务器): %v", err)
		return
	}
	
	// 如果连接成功，测试获取Topic列表
	topics, err := client.GetTopics()
	if err != nil {
		t.Errorf("获取RocketMQ Topic列表失败: %v", err)
		return
	}
	
	fmt.Printf("找到 %d 个RocketMQ Topics:\n", len(topics))
	for _, topic := range topics {
		fmt.Printf("- %s (队列数: %d)\n", topic.Name, topic.QueueNum)
	}
	
	// 测试获取订阅组列表
	groups, err := client.GetSubscriptionGroups()
	if err != nil {
		t.Errorf("获取RocketMQ订阅组列表失败: %v", err)
		return
	}
	
	fmt.Printf("找到 %d 个RocketMQ订阅组:\n", len(groups))
	for _, group := range groups {
		fmt.Printf("- %s (状态: %s)\n", group.Name, group.State)
	}
}

// TestClientCreation 测试客户端创建
func TestClientCreation(t *testing.T) {
	// 测试Kafka客户端创建
	kafkaClient := clients.NewKafkaClient("localhost:9092", "user", "pass")
	if kafkaClient == nil {
		t.Error("Kafka客户端创建失败")
	}
	kafkaClient.Close()
	
	// 测试RocketMQ客户端创建
	rocketMQClient := clients.NewRocketMQClient("localhost:9876", "user", "pass")
	if rocketMQClient == nil {
		t.Error("RocketMQ客户端创建失败")
	}
	rocketMQClient.Close()
}
