#!/bin/bash

# Topic管理缓存功能测试脚本
# 用于测试新增的缓存逻辑是否正常工作

BASE_URL="http://localhost:3000/api/v1"
CONFIG_ID=1  # 测试用的配置ID，请根据实际情况修改

echo "=== Topic管理缓存功能测试 ==="
echo ""

# 1. 测试获取Topic配置列表
echo "1. 测试获取Topic配置列表..."
curl -s -X GET "${BASE_URL}/topic/config?with_env_info=true" | jq '.'
echo ""

# 2. 测试首次获取Topic和Group信息（应该从实时源获取并缓存）
echo "2. 测试首次获取Topic和Group信息（从实时源获取并缓存）..."
curl -s -X GET "${BASE_URL}/topic/config/${CONFIG_ID}/topics-groups" | jq '.'
echo ""

# 3. 测试再次获取Topic和Group信息（应该从缓存获取）
echo "3. 测试再次获取Topic和Group信息（从缓存获取）..."
curl -s -X GET "${BASE_URL}/topic/config/${CONFIG_ID}/topics-groups" | jq '.'
echo ""

# 4. 测试强制刷新缓存
echo "4. 测试强制刷新缓存..."
curl -s -X POST "${BASE_URL}/topic/config/${CONFIG_ID}/topics-groups/refresh" | jq '.'
echo ""

# 5. 测试获取刷新后的缓存数据
echo "5. 测试获取刷新后的缓存数据..."
curl -s -X GET "${BASE_URL}/topic/config/${CONFIG_ID}/topics-groups" | jq '.'
echo ""

echo "=== 测试完成 ==="
echo ""
echo "注意事项："
echo "1. 请确保后端服务已启动（端口3000）"
echo "2. 请确保数据库中存在ID为${CONFIG_ID}的Topic配置"
echo "3. 请确保已执行数据库迁移脚本创建缓存表"
echo "4. 如需测试其他配置，请修改脚本中的CONFIG_ID变量"
